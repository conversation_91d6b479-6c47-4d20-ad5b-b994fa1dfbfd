import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'screens/splash_screen.dart';
import 'screens/plugin_marketplace_home_screen.dart';
import 'screens/multi_image_input_screen.dart';
import 'screens/membership_screen.dart';
import 'screens/settings_screen.dart';
import 'screens/history_screen.dart';
import 'providers/app_state_provider.dart';

class NanoBananaApp extends StatelessWidget {
    const NanoBananaApp({super.key});

    @override
    Widget build(BuildContext context) {
        return MultiProvider(
            providers: [
                ChangeNotifierProvider(create: (_) => AppStateProvider()),
            ],
            child: Consumer<AppStateProvider>(
                builder: (context, appState, child) {
                    return MaterialApp.router(
                        title: 'NanoBanana AI',
                        debugShowCheckedModeBanner: false,
                        theme: _buildLightTheme(),
                        darkTheme: _buildDarkTheme(),
                        themeMode: appState.themeMode,
                        routerConfig: _router,
                    );
                },
            ),
        );
    }

    static final GoRouter _router = GoRouter(
        initialLocation: '/splash',
        routes: [
            GoRoute(
                path: '/splash',
                builder: (context, state) => const SplashScreen(),
            ),
            GoRoute(
                path: '/home',
                builder: (context, state) => const PluginMarketplaceHomeScreen(),
            ),
            GoRoute(
                path: '/history',
                builder: (context, state) => const HistoryScreen(),
            ),
            GoRoute(
                path: '/create',
                builder: (context, state) {
                    final extra = state.extra as Map<String, dynamic>?;
                    final prompt = extra?['prompt'] as String?;
                    return MultiImageInputScreen(initialPrompt: prompt);
                },
            ),
            GoRoute(
                path: '/membership',
                builder: (context, state) => const MembershipScreen(),
            ),
            GoRoute(
                path: '/settings',
                builder: (context, state) => const SettingsScreen(),
            ),
        ],
    );

    ThemeData _buildLightTheme() {
        return ThemeData(
            useMaterial3: true,
            colorScheme: ColorScheme.fromSeed(
                seedColor: const Color(0xFF7B61FF),
                brightness: Brightness.light,
            ),
            scaffoldBackgroundColor: const Color(0xFFFDFCFB),
            appBarTheme: const AppBarTheme(
                backgroundColor: Colors.transparent,
                elevation: 0,
                systemOverlayStyle: SystemUiOverlayStyle.dark,
                titleTextStyle: TextStyle(
                    color: Colors.black,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                ),
                iconTheme: IconThemeData(color: Colors.black),
            ),
            bottomNavigationBarTheme: const BottomNavigationBarThemeData(
                backgroundColor: Colors.white,
                selectedItemColor: Color(0xFF7B61FF),
                unselectedItemColor: Colors.grey,
                type: BottomNavigationBarType.fixed,
            ),
            elevatedButtonTheme: ElevatedButtonThemeData(
                style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF7B61FF),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                    ),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                    ),
                ),
            ),
        );
    }

    ThemeData _buildDarkTheme() {
        return ThemeData(
            useMaterial3: true,
            colorScheme: ColorScheme.fromSeed(
                seedColor: const Color(0xFF7B61FF),
                brightness: Brightness.dark,
            ),
            scaffoldBackgroundColor: const Color(0xFF1A1A1A),
        );
    }
}
