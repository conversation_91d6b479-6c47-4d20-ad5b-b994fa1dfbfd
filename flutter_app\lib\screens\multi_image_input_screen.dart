import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import '../providers/app_state_provider.dart';
import '../widgets/bottom_navigation.dart';

class MultiImageInputScreen extends StatefulWidget {
    final String? initialPrompt;

    const MultiImageInputScreen({super.key, this.initialPrompt});

    @override
    State<MultiImageInputScreen> createState() => _MultiImageInputScreenState();
}

class _MultiImageInputScreenState extends State<MultiImageInputScreen> {
    final TextEditingController _promptController = TextEditingController();
    final ImagePicker _picker = ImagePicker();
    List<String> _selectedImages = [];
    bool _isGenerating = false;

    // Firebase Cloud Functions URLs
    static const String _textToImageUrl = "https://us-central1-nano-banana-60f5a.cloudfunctions.net/generate_image_from_text";
    static const String _imageWithReferencesUrl = "https://us-central1-nano-banana-60f5a.cloudfunctions.net/generate_image_with_references";

    @override
    void initState() {
        super.initState();
        final appState = Provider.of<AppStateProvider>(context, listen: false);
        // Use the passed prompt or fallback to the app state prompt
        _promptController.text = widget.initialPrompt ?? appState.currentPrompt;
        _selectedImages = List.from(appState.selectedImages);
    }

    @override
    Widget build(BuildContext context) {
        return Scaffold(
            backgroundColor: const Color(0xFFF9F8FC),
            body: SafeArea(
                child: Column(
                    children: [
                        _buildHeader(),
                        Expanded(
                            child: SingleChildScrollView(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                        _buildReferenceImagesSection(),
                                        const SizedBox(height: 32),
                                        _buildPromptSection(),
                                        const SizedBox(height: 100), // Space for bottom button
                                    ],
                                ),
                            ),
                        ),
                    ],
                ),
            ),
            bottomNavigationBar: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                    _buildGenerateButton(),
                    Consumer<AppStateProvider>(
                        builder: (context, appState, child) {
                            return AdaptiveBottomNavigation(
                                currentIndex: 2,
                                onTap: appState.setBottomNavIndex,
                            );
                        },
                    ),
                ],
            ),
        );
    }

    Widget _buildHeader() {
        return Container(
            color: const Color(0xFFF9F8FC),
            child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                    children: [
                        IconButton(
                            onPressed: () => context.go('/home'),
                            icon: const Icon(
                                Icons.arrow_back_ios,
                                color: Color(0xFF100D1B),
                            ),
                        ),
                        const Expanded(
                            child: Text(
                                'Create',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: Color(0xFF100D1B),
                                ),
                            ),
                        ),
                        const SizedBox(width: 48), // Balance the back button
                    ],
                ),
            ),
        );
    }

    Widget _buildReferenceImagesSection() {
        return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
                const Text(
                    'Reference Images',
                    style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF100D1B),
                    ),
                ),
                const SizedBox(height: 4),
                const Text(
                    'Select the images you want to combine.',
                    style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                    ),
                ),
                const SizedBox(height: 16),
                GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        crossAxisSpacing: 16,
                        mainAxisSpacing: 16,
                        childAspectRatio: 1.0,
                    ),
                    itemCount: _selectedImages.length + 1,
                    itemBuilder: (context, index) {
                        if (index < _selectedImages.length) {
                            return _buildSelectedImageCard(_selectedImages[index], index);
                        } else {
                            return _buildAddImageCard();
                        }
                    },
                ),
            ],
        );
    }

    Widget _buildSelectedImageCard(String imagePath, int index) {
        return Container(
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                    BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                    ),
                ],
            ),
            child: Stack(
                children: [
                    Container(
                        width: double.infinity,
                        height: double.infinity,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16),
                            image: DecorationImage(
                                image: FileImage(File(imagePath)),
                                fit: BoxFit.cover,
                            ),
                        ),
                    ),
                    // 刪除按鈕
                    Positioned(
                        top: 8,
                        right: 8,
                        child: GestureDetector(
                            onTap: () {
                                setState(() {
                                    _selectedImages.removeAt(index);
                                });

                                // Update app state
                                final appState = Provider.of<AppStateProvider>(context, listen: false);
                                appState.removeSelectedImage(imagePath);
                            },
                            child: Container(
                                padding: const EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                    color: Colors.red,
                                    borderRadius: BorderRadius.circular(12),
                                ),
                                child: const Icon(
                                    Icons.close,
                                    color: Colors.white,
                                    size: 16,
                                ),
                            ),
                        ),
                    ),
                ],
            ),
        );
    }

    Widget _buildAddImageCard() {
        return GestureDetector(
            onTap: _pickImage,
            child: Container(
                decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                        color: Colors.grey[300]!,
                        width: 2,
                        style: BorderStyle.solid,
                    ),
                ),
                child: const Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                        Icon(
                            Icons.add_photo_alternate_outlined,
                            size: 48,
                            color: Colors.grey,
                        ),
                        SizedBox(height: 8),
                        Text(
                            'Add Image',
                            style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Colors.grey,
                            ),
                        ),
                    ],
                ),
            ),
        );
    }

    Widget _buildPromptSection() {
        return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
                const Text(
                    'Prompt',
                    style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF100D1B),
                    ),
                ),
                const SizedBox(height: 16),
                Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                            BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 10,
                                offset: const Offset(0, 2),
                            ),
                        ],
                    ),
                    child: Stack(
                        children: [
                            TextField(
                                controller: _promptController,
                                maxLines: 4,
                                decoration: const InputDecoration(
                                    hintText: 'Describe the image you want to create...',
                                    hintStyle: TextStyle(color: Colors.grey),
                                    border: InputBorder.none,
                                    contentPadding: EdgeInsets.fromLTRB(16, 16, 48, 16),
                                ),
                                style: const TextStyle(
                                    fontSize: 16,
                                    color: Color(0xFF100D1B),
                                ),
                                onChanged: (value) {
                                    final appState = Provider.of<AppStateProvider>(
                                        context, 
                                        listen: false,
                                    );
                                    appState.setPrompt(value);
                                },
                            ),
                            Positioned(
                                bottom: 12,
                                right: 12,
                                child: Container(
                                    width: 32,
                                    height: 32,
                                    decoration: BoxDecoration(
                                        color: Colors.grey[100],
                                        borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: IconButton(
                                        onPressed: () {
                                            // AI suggestion functionality
                                        },
                                        icon: const Icon(
                                            Icons.auto_awesome,
                                            size: 16,
                                            color: Colors.grey,
                                        ),
                                    ),
                                ),
                            ),
                        ],
                    ),
                ),
            ],
        );
    }

    Widget _buildGenerateButton() {
        return Container(
            padding: const EdgeInsets.all(16.0),
            child: SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton(
                    onPressed: _promptController.text.isNotEmpty && !_isGenerating
                        ? _generateImage
                        : null,
                    style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF3713EC),
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                        ),
                        elevation: 8,
                        shadowColor: const Color(0xFF3713EC).withOpacity(0.3),
                    ),
                    child: const Text(
                        'Generate',
                        style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                        ),
                    ),
                ),
            ),
        );
    }

    Future<void> _pickImage() async {
        try {
            final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
            if (image != null) {
                setState(() {
                    _selectedImages.add(image.path);
                });

                final appState = Provider.of<AppStateProvider>(context, listen: false);
                appState.addSelectedImage(image.path);
            }
        } catch (e) {
            ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Error picking image: $e')),
            );
        }
    }

    Future<String> _imageToBase64(String imagePath) async {
        final bytes = await File(imagePath).readAsBytes();
        return base64Encode(bytes);
    }

    String _getMimeType(String imagePath) {
        final extension = imagePath.split('.').last.toLowerCase();
        switch (extension) {
            case 'jpg':
            case 'jpeg':
                return 'image/jpeg';
            case 'png':
                return 'image/png';
            case 'gif':
                return 'image/gif';
            case 'webp':
                return 'image/webp';
            default:
                return 'image/jpeg';
        }
    }

    Future<void> _generateImage() async {
        if (_isGenerating) return;

        setState(() {
            _isGenerating = true;
        });

        // Show loading dialog
        showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) => const Center(
                child: CircularProgressIndicator(),
            ),
        );

        try {
            await _callGeminiAPI();
        } catch (e) {
            if (mounted) {
                Navigator.of(context).pop(); // Close loading dialog
                ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                        content: Text('Error generating image: $e'),
                        backgroundColor: Colors.red,
                    ),
                );
            }
        } finally {
            if (mounted) {
                setState(() {
                    _isGenerating = false;
                });
            }
        }
    }

    Future<void> _callGeminiAPI() async {
        String apiUrl;
        Map<String, dynamic> requestBody;

        // 根據是否有選擇的圖片決定使用哪個 Cloud Function
        if (_selectedImages.isEmpty) {
            // 純文字生成
            apiUrl = _textToImageUrl;
            requestBody = {
                "prompt": _promptController.text
            };
        } else {
            // 使用參考圖片生成
            apiUrl = _imageWithReferencesUrl;

            // 準備圖片數據
            List<Map<String, dynamic>> imagesData = [];
            for (String imagePath in _selectedImages) {
                final base64Image = await _imageToBase64(imagePath);
                final mimeType = _getMimeType(imagePath);

                imagesData.add({
                    "data": base64Image,
                    "mime_type": mimeType
                });
            }

            requestBody = {
                "prompt": _promptController.text,
                "images": imagesData
            };
        }

        // 發送請求到 Firebase Cloud Function
        final response = await http.post(
            Uri.parse(apiUrl),
            headers: {
                'Content-Type': 'application/json',
            },
            body: jsonEncode(requestBody),
        );

        if (mounted) {
            Navigator.of(context).pop(); // Close loading dialog
        }

        if (response.statusCode == 200) {
            final responseData = jsonDecode(response.body);
            await _handleCloudFunctionResponse(responseData);
        } else {
            throw Exception('Cloud Function request failed: ${response.statusCode} - ${response.body}');
        }
    }

    Future<void> _handleCloudFunctionResponse(Map<String, dynamic> responseData) async {
        try {
            if (responseData['success'] == true) {
                final images = responseData['images'] as List?;
                final text = responseData['text'] as String?;

                // 處理文字回應
                if (text != null && text.isNotEmpty) {
                    print('Cloud Function Response Text: $text');
                }

                // 處理圖片回應
                if (images != null && images.isNotEmpty) {
                    for (var imageData in images) {
                        final base64Data = imageData['data'] as String;
                        final mimeType = imageData['mime_type'] as String;

                        // 將 base64 圖片數據保存到本地
                        await _saveGeneratedImage(base64Data, mimeType);
                    }

                    if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                                content: Text('成功生成 ${images.length} 張圖片！'),
                                backgroundColor: Colors.green,
                            ),
                        );
                    }
                } else {
                    // 如果沒有找到圖片數據
                    if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                                content: Text('沒有收到圖片數據'),
                                backgroundColor: Colors.orange,
                            ),
                        );
                    }
                }
            } else {
                // 處理錯誤響應
                final error = responseData['error'] ?? '未知錯誤';
                if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                            content: Text('生成失敗: $error'),
                            backgroundColor: Colors.red,
                        ),
                    );
                }
            }
        } catch (e) {
            if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                        content: Text('處理響應時發生錯誤: $e'),
                        backgroundColor: Colors.red,
                    ),
                );
            }
        }
    }

    Future<void> _saveGeneratedImage(String base64Data, String mimeType) async {
        try {
            final bytes = base64Decode(base64Data);
            final extension = mimeType.split('/').last;
            final timestamp = DateTime.now().millisecondsSinceEpoch;
            final fileName = 'generated_image_$timestamp.$extension';

            // 這裡可以根據需要保存到不同的位置
            // 暫時只顯示成功消息，實際保存邏輯可以根據需求調整
            print('Generated image saved: $fileName (${bytes.length} bytes)');

        } catch (e) {
            print('Error saving generated image: $e');
            throw e;
        }
    }
}