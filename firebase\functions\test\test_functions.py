#!/usr/bin/env python3
"""
測試 Firebase Cloud Functions 的腳本
"""

import base64
import json
import requests
from PIL import Image
from io import BytesIO

def test_text_to_image():
    """測試純文字生成圖片功能"""
    print("測試純文字生成圖片...")
    
    # 測試數據
    test_data = {
        "prompt": "一隻可愛的小貓咪在花園裡玩耍"
    }
    
    # 本地測試 URL（當 functions 在本地運行時）
    url = "http://localhost:5001/nano-banana-60f5a/us-central1/generate_image_from_text"
    
    try:
        response = requests.post(url, json=test_data)
        print(f"狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("成功！")
            print(f"文字回應: {result.get('text', 'N/A')}")
            print(f"生成的圖片數量: {len(result.get('images', []))}")
            
            # 保存生成的圖片
            for i, img_data in enumerate(result.get('images', [])):
                save_image_from_base64(img_data['data'], f"generated_text_{i}.jpg")
        else:
            print(f"錯誤: {response.text}")
            
    except Exception as e:
        print(f"測試失敗: {e}")


def test_image_with_references():
    """測試使用參考圖片生成圖片功能"""
    print("\n測試使用參考圖片生成圖片...")
    
    # 創建一個測試圖片
    test_image = create_test_image()
    img_base64 = image_to_base64(test_image)
    
    # 測試數據
    test_data = {
        "prompt": "將這張圖片變成卡通風格",
        "images": [
            {
                "data": img_base64,
                "mime_type": "image/jpeg"
            }
        ]
    }
    
    # 本地測試 URL
    url = "http://localhost:5001/nano-banana-60f5a/us-central1/generate_image_with_references"
    
    try:
        response = requests.post(url, json=test_data)
        print(f"狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("成功！")
            print(f"文字回應: {result.get('text', 'N/A')}")
            print(f"生成的圖片數量: {len(result.get('images', []))}")
            
            # 保存生成的圖片
            for i, img_data in enumerate(result.get('images', [])):
                save_image_from_base64(img_data['data'], f"generated_ref_{i}.jpg")
        else:
            print(f"錯誤: {response.text}")
            
    except Exception as e:
        print(f"測試失敗: {e}")


def create_test_image():
    """創建一個測試圖片"""
    # 創建一個簡單的彩色圖片
    img = Image.new('RGB', (256, 256), color='lightblue')
    return img


def image_to_base64(image):
    """將 PIL Image 轉換為 base64 字符串"""
    buffer = BytesIO()
    image.save(buffer, format='JPEG')
    img_bytes = buffer.getvalue()
    return base64.b64encode(img_bytes).decode('utf-8')


def save_image_from_base64(base64_data, filename):
    """從 base64 數據保存圖片"""
    try:
        img_bytes = base64.b64decode(base64_data)
        img = Image.open(BytesIO(img_bytes))
        img.save(filename)
        print(f"圖片已保存: {filename}")
    except Exception as e:
        print(f"保存圖片失敗: {e}")


if __name__ == "__main__":
    print("Firebase Cloud Functions 測試")
    print("=" * 50)
    
    # 測試純文字生成
    test_text_to_image()
    
    # 測試參考圖片生成
    test_image_with_references()
    
    print("\n測試完成！")
