{"buildFiles": ["C:\\development\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\nano_banana\\flutter_app\\build\\.cxx\\debug\\11fz6w56\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\nano_banana\\flutter_app\\build\\.cxx\\debug\\11fz6w56\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}