import 'dart:convert';
import 'package:flutter/material.dart';

/// 生成歷史記錄的數據模型
class GenerationHistory {
    final String id;
    final DateTime createdAt;
    final String prompt;
    final List<String> referenceImages;
    final List<GeneratedImage> generatedImages;
    final GenerationStatus status;
    final String? errorMessage;

    GenerationHistory({
        required this.id,
        required this.createdAt,
        required this.prompt,
        required this.referenceImages,
        required this.generatedImages,
        required this.status,
        this.errorMessage,
    });

    /// 從 JSON 創建實例
    factory GenerationHistory.fromJson(Map<String, dynamic> json) {
        return GenerationHistory(
            id: json['id'] as String,
            createdAt: DateTime.parse(json['createdAt'] as String),
            prompt: json['prompt'] as String,
            referenceImages: List<String>.from(json['referenceImages'] as List),
            generatedImages: (json['generatedImages'] as List)
                .map((item) => GeneratedImage.fromJson(item))
                .toList(),
            status: GenerationStatus.values.firstWhere(
                (e) => e.name == json['status'],
                orElse: () => GenerationStatus.failed,
            ),
            errorMessage: json['errorMessage'] as String?,
        );
    }

    /// 轉換為 JSON
    Map<String, dynamic> toJson() {
        return {
            'id': id,
            'createdAt': createdAt.toIso8601String(),
            'prompt': prompt,
            'referenceImages': referenceImages,
            'generatedImages': generatedImages.map((img) => img.toJson()).toList(),
            'status': status.name,
            'errorMessage': errorMessage,
        };
    }

    /// 創建副本
    GenerationHistory copyWith({
        String? id,
        DateTime? createdAt,
        String? prompt,
        List<String>? referenceImages,
        List<GeneratedImage>? generatedImages,
        GenerationStatus? status,
        String? errorMessage,
    }) {
        return GenerationHistory(
            id: id ?? this.id,
            createdAt: createdAt ?? this.createdAt,
            prompt: prompt ?? this.prompt,
            referenceImages: referenceImages ?? this.referenceImages,
            generatedImages: generatedImages ?? this.generatedImages,
            status: status ?? this.status,
            errorMessage: errorMessage ?? this.errorMessage,
        );
    }

    /// 格式化創建時間
    String get formattedTime {
        final now = DateTime.now();
        final difference = now.difference(createdAt);

        if (difference.inMinutes < 1) {
            return '剛剛';
        } else if (difference.inHours < 1) {
            return '${difference.inMinutes} 分鐘前';
        } else if (difference.inDays < 1) {
            return '${difference.inHours} 小時前';
        } else if (difference.inDays < 7) {
            return '${difference.inDays} 天前';
        } else {
            return '${createdAt.month}/${createdAt.day} ${createdAt.hour.toString().padLeft(2, '0')}:${createdAt.minute.toString().padLeft(2, '0')}';
        }
    }

    /// 是否有參考圖片
    bool get hasReferenceImages => referenceImages.isNotEmpty;

    /// 生成的圖片數量
    int get generatedImageCount => generatedImages.length;
}

/// 生成的圖片數據模型
class GeneratedImage {
    final String id;
    final String localPath;
    final String? originalBase64;
    final String mimeType;
    final DateTime createdAt;

    GeneratedImage({
        required this.id,
        required this.localPath,
        this.originalBase64,
        required this.mimeType,
        required this.createdAt,
    });

    /// 從 JSON 創建實例
    factory GeneratedImage.fromJson(Map<String, dynamic> json) {
        return GeneratedImage(
            id: json['id'] as String,
            localPath: json['localPath'] as String,
            originalBase64: json['originalBase64'] as String?,
            mimeType: json['mimeType'] as String,
            createdAt: DateTime.parse(json['createdAt'] as String),
        );
    }

    /// 轉換為 JSON
    Map<String, dynamic> toJson() {
        return {
            'id': id,
            'localPath': localPath,
            'originalBase64': originalBase64,
            'mimeType': mimeType,
            'createdAt': createdAt.toIso8601String(),
        };
    }
}

/// 生成狀態枚舉
enum GenerationStatus {
    generating,  // 生成中
    completed,   // 完成
    failed,      // 失敗
}

/// 生成狀態的擴展方法
extension GenerationStatusExtension on GenerationStatus {
    /// 狀態顯示文字
    String get displayText {
        switch (this) {
            case GenerationStatus.generating:
                return '生成中...';
            case GenerationStatus.completed:
                return '已完成';
            case GenerationStatus.failed:
                return '生成失敗';
        }
    }

    /// 狀態顏色
    Color get color {
        switch (this) {
            case GenerationStatus.generating:
                return const Color(0xFF3713EC);
            case GenerationStatus.completed:
                return const Color(0xFF4CAF50);
            case GenerationStatus.failed:
                return const Color(0xFFF44336);
        }
    }
}
