import 'package:flutter/material.dart';
import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import '../models/generation_history.dart';

class AppStateProvider extends ChangeNotifier {
    ThemeMode _themeMode = ThemeMode.system;
    int _currentBottomNavIndex = 0;
    final List<String> _selectedImages = [];
    String _currentPrompt = '';
    final List<GenerationHistory> _generationHistory = [];
    bool _isHistoryLoaded = false;

    // Getters
    ThemeMode get themeMode => _themeMode;
    int get currentBottomNavIndex => _currentBottomNavIndex;
    List<String> get selectedImages => _selectedImages;
    String get currentPrompt => _currentPrompt;
    List<GenerationHistory> get generationHistory => _generationHistory;
    bool get isHistoryLoaded => _isHistoryLoaded;

    // Theme management
    void setThemeMode(ThemeMode mode) {
        _themeMode = mode;
        notifyListeners();
    }

    void toggleTheme() {
        _themeMode = _themeMode == ThemeMode.light 
            ? ThemeMode.dark 
            : ThemeMode.light;
        notifyListeners();
    }

    // Bottom navigation
    void setBottomNavIndex(int index) {
        _currentBottomNavIndex = index;
        notifyListeners();
    }

    // Image selection
    void addSelectedImage(String imagePath) {
        if (!_selectedImages.contains(imagePath)) {
            _selectedImages.add(imagePath);
            notifyListeners();
        }
    }

    void removeSelectedImage(String imagePath) {
        _selectedImages.remove(imagePath);
        notifyListeners();
    }

    void clearSelectedImages() {
        _selectedImages.clear();
        notifyListeners();
    }

    // Prompt management
    void setPrompt(String prompt) {
        _currentPrompt = prompt;
        notifyListeners();
    }

    void clearPrompt() {
        _currentPrompt = '';
        notifyListeners();
    }

    // Generation history management
    /// 添加新的生成記錄
    Future<void> addGenerationHistory(GenerationHistory history) async {
        _generationHistory.insert(0, history); // 最新的在前面
        notifyListeners();
        await _saveHistoryToFile();
    }

    /// 更新現有的生成記錄
    Future<void> updateGenerationHistory(String id, GenerationHistory updatedHistory) async {
        final index = _generationHistory.indexWhere((h) => h.id == id);
        if (index != -1) {
            _generationHistory[index] = updatedHistory;
            notifyListeners();
            await _saveHistoryToFile();
        }
    }

    /// 刪除生成記錄
    Future<void> removeGenerationHistory(String id) async {
        _generationHistory.removeWhere((h) => h.id == id);
        notifyListeners();
        await _saveHistoryToFile();
    }

    /// 清空所有歷史記錄
    Future<void> clearGenerationHistory() async {
        _generationHistory.clear();
        notifyListeners();
        await _saveHistoryToFile();
    }

    /// 從文件加載歷史記錄
    Future<void> loadHistoryFromFile() async {
        if (_isHistoryLoaded) return;

        try {
            final directory = await getApplicationDocumentsDirectory();
            final file = File('${directory.path}/generation_history.json');

            if (await file.exists()) {
                final jsonString = await file.readAsString();
                final List<dynamic> jsonList = jsonDecode(jsonString);

                _generationHistory.clear();
                _generationHistory.addAll(
                    jsonList.map((json) => GenerationHistory.fromJson(json)).toList()
                );
            }

            _isHistoryLoaded = true;
            notifyListeners();
        } catch (e) {
            print('Error loading history from file: $e');
            _isHistoryLoaded = true;
            notifyListeners();
        }
    }

    /// 保存歷史記錄到文件
    Future<void> _saveHistoryToFile() async {
        try {
            final directory = await getApplicationDocumentsDirectory();
            final file = File('${directory.path}/generation_history.json');

            final jsonList = _generationHistory.map((h) => h.toJson()).toList();
            await file.writeAsString(jsonEncode(jsonList));
        } catch (e) {
            print('Error saving history to file: $e');
        }
    }

    /// 創建新的生成記錄（生成開始時調用）
    Future<String> startGeneration(String prompt, List<String> referenceImages) async {
        final id = DateTime.now().millisecondsSinceEpoch.toString();
        final history = GenerationHistory(
            id: id,
            createdAt: DateTime.now(),
            prompt: prompt,
            referenceImages: List.from(referenceImages),
            generatedImages: [],
            status: GenerationStatus.generating,
        );

        await addGenerationHistory(history);
        return id;
    }

    /// 完成生成（生成成功時調用）
    Future<void> completeGeneration(String id, List<GeneratedImage> generatedImages) async {
        final index = _generationHistory.indexWhere((h) => h.id == id);
        if (index != -1) {
            final updatedHistory = _generationHistory[index].copyWith(
                status: GenerationStatus.completed,
                generatedImages: generatedImages,
            );
            await updateGenerationHistory(id, updatedHistory);
        }
    }

    /// 標記生成失敗（生成失敗時調用）
    Future<void> failGeneration(String id, String errorMessage) async {
        final index = _generationHistory.indexWhere((h) => h.id == id);
        if (index != -1) {
            final updatedHistory = _generationHistory[index].copyWith(
                status: GenerationStatus.failed,
                errorMessage: errorMessage,
            );
            await updateGenerationHistory(id, updatedHistory);
        }
    }
}
