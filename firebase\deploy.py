#!/usr/bin/env python3
"""
Firebase Cloud Functions 部署腳本
"""

import subprocess
import sys
import os

def run_command(command, cwd=None):
    """執行命令並返回結果"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd,
            capture_output=True, 
            text=True
        )
        
        if result.returncode == 0:
            print(f"✅ 成功: {command}")
            if result.stdout:
                print(result.stdout)
        else:
            print(f"❌ 失敗: {command}")
            if result.stderr:
                print(result.stderr)
            return False
        
        return True
    except Exception as e:
        print(f"❌ 執行命令時發生錯誤: {e}")
        return False


def deploy_functions():
    """部署 Cloud Functions"""
    print("🚀 開始部署 Firebase Cloud Functions...")
    
    # 確保在正確的目錄
    firebase_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 部署 functions
    if run_command("firebase deploy --only functions", cwd=firebase_dir):
        print("🎉 Cloud Functions 部署成功！")
        print("\n📝 部署的函數:")
        print("1. generate_image_from_text - 純文字生成圖片")
        print("2. generate_image_with_references - 使用參考圖片生成圖片")
        
        print("\n🔗 函數 URL 格式:")
        print("https://us-central1-nano-banana-60f5a.cloudfunctions.net/generate_image_from_text")
        print("https://us-central1-nano-banana-60f5a.cloudfunctions.net/generate_image_with_references")
        
        return True
    else:
        print("❌ 部署失敗")
        return False


def test_local():
    """本地測試 functions"""
    print("🧪 啟動本地測試環境...")
    
    firebase_dir = os.path.dirname(os.path.abspath(__file__))
    
    print("執行以下命令來啟動本地模擬器:")
    print(f"cd {firebase_dir}")
    print("firebase emulators:start --only functions")
    print("\n然後在另一個終端運行:")
    print("python functions/test_functions.py")


def main():
    """主函數"""
    if len(sys.argv) < 2:
        print("使用方法:")
        print("python deploy.py deploy  - 部署到 Firebase")
        print("python deploy.py test    - 啟動本地測試")
        return
    
    action = sys.argv[1].lower()
    
    if action == "deploy":
        deploy_functions()
    elif action == "test":
        test_local()
    else:
        print("未知的操作。使用 'deploy' 或 'test'")


if __name__ == "__main__":
    main()
