# Nano Banana Firebase Cloud Functions

這個項目使用 Firebase Cloud Functions 來安全地處理 Gemini API 調用，避免在客戶端暴露 API Key。

## 功能

### 1. 純文字生成圖片 (`generate_image_from_text`)
- **URL**: `https://us-central1-nano-banana-60f5a.cloudfunctions.net/generate_image_from_text`
- **方法**: POST
- **功能**: 根據文字描述生成圖片

**請求格式**:
```json
{
    "prompt": "一隻可愛的小貓咪在花園裡玩耍"
}
```

**響應格式**:
```json
{
    "success": true,
    "images": [
        {
            "data": "base64編碼的圖片數據",
            "mime_type": "image/jpeg"
        }
    ],
    "text": "API返回的文字描述"
}
```

### 2. 使用參考圖片生成圖片 (`generate_image_with_references`)
- **URL**: `https://us-central1-nano-banana-60f5a.cloudfunctions.net/generate_image_with_references`
- **方法**: POST
- **功能**: 基於參考圖片和文字描述生成新圖片

**請求格式**:
```json
{
    "prompt": "將這張圖片變成卡通風格",
    "images": [
        {
            "data": "base64編碼的圖片數據",
            "mime_type": "image/jpeg"
        }
    ]
}
```

**響應格式**:
```json
{
    "success": true,
    "images": [
        {
            "data": "base64編碼的圖片數據",
            "mime_type": "image/jpeg"
        }
    ],
    "text": "API返回的文字描述"
}
```

## 安裝和部署

### 1. 安裝依賴
```bash
cd firebase/functions
# 激活虛擬環境
.\venv\Scripts\activate  # Windows
source venv/bin/activate  # macOS/Linux

# 安裝 Python 依賴
pip install -r requirements.txt
```

### 2. 本地測試
```bash
# 啟動 Firebase 模擬器
cd firebase
firebase emulators:start --only functions

# 在另一個終端運行測試
cd firebase/functions
python test_functions.py
```

### 3. 部署到 Firebase
```bash
cd firebase
firebase deploy --only functions

# 或使用部署腳本
python deploy.py deploy
```

## 安全性

- ✅ API Key 安全存儲在 Firebase Cloud Functions 中
- ✅ 客戶端無法直接訪問 Gemini API
- ✅ 支持 CORS 跨域請求
- ✅ 請求驗證和錯誤處理

## Flutter 應用集成

Flutter 應用已經修改為調用 Firebase Cloud Functions 而不是直接調用 Gemini API：

```dart
// 舊的直接 API 調用（不安全）
// static const String _apiKey = "YOUR_API_KEY";
// static const String _apiUrl = "https://generativelanguage.googleapis.com/...";

// 新的 Firebase Cloud Functions 調用（安全）
static const String _textToImageUrl = "https://us-central1-nano-banana-60f5a.cloudfunctions.net/generate_image_from_text";
static const String _imageWithReferencesUrl = "https://us-central1-nano-banana-60f5a.cloudfunctions.net/generate_image_with_references";
```

## 文件結構

```
firebase/
├── functions/
│   ├── main.py              # Cloud Functions 主文件
│   ├── requirements.txt     # Python 依賴
│   ├── test_functions.py    # 測試腳本
│   └── venv/               # Python 虛擬環境
├── deploy.py               # 部署腳本
└── README.md              # 說明文件
```

## 故障排除

### 1. 部署失敗
- 確保已經登錄 Firebase: `firebase login`
- 確保選擇了正確的項目: `firebase use nano-banana-60f5a`

### 2. 函數調用失敗
- 檢查函數 URL 是否正確
- 確保請求格式符合 API 規範
- 查看 Firebase Console 中的函數日誌

### 3. 圖片處理錯誤
- 確保圖片是有效的 base64 格式
- 支持的圖片格式: JPEG, PNG, GIF, WebP
- 檢查圖片大小是否過大

## 成本控制

- 設置了最大實例數限制: `max_instances=10`
- 使用按需計費模式
- 建議監控 Firebase 使用量和費用
