import 'package:flutter/material.dart';
import 'dart:math' as math;

/// 美觀的圖片生成載入對話框
class GenerationLoadingDialog extends StatefulWidget {
    final String? message;
    final VoidCallback? onCancel;

    const GenerationLoadingDialog({
        super.key,
        this.message,
        this.onCancel,
    });

    @override
    State<GenerationLoadingDialog> createState() => _GenerationLoadingDialogState();
}

class _GenerationLoadingDialogState extends State<GenerationLoadingDialog>
    with TickerProviderStateMixin {
    late AnimationController _rotationController;
    late AnimationController _pulseController;
    late AnimationController _fadeController;
    
    late Animation<double> _rotationAnimation;
    late Animation<double> _pulseAnimation;
    late Animation<double> _fadeAnimation;

    int _currentMessageIndex = 0;
    final List<String> _loadingMessages = [
        '正在分析您的提示詞...',
        '正在處理參考圖片...',
        'AI 正在創作中...',
        '正在優化圖片品質...',
        '即將完成...',
    ];

    @override
    void initState() {
        super.initState();
        
        // 旋轉動畫控制器
        _rotationController = AnimationController(
            duration: const Duration(seconds: 2),
            vsync: this,
        );
        _rotationAnimation = Tween<double>(
            begin: 0,
            end: 2 * math.pi,
        ).animate(CurvedAnimation(
            parent: _rotationController,
            curve: Curves.linear,
        ));

        // 脈衝動畫控制器
        _pulseController = AnimationController(
            duration: const Duration(milliseconds: 1500),
            vsync: this,
        );
        _pulseAnimation = Tween<double>(
            begin: 0.8,
            end: 1.2,
        ).animate(CurvedAnimation(
            parent: _pulseController,
            curve: Curves.easeInOut,
        ));

        // 淡入淡出動畫控制器
        _fadeController = AnimationController(
            duration: const Duration(milliseconds: 800),
            vsync: this,
        );
        _fadeAnimation = Tween<double>(
            begin: 0.0,
            end: 1.0,
        ).animate(CurvedAnimation(
            parent: _fadeController,
            curve: Curves.easeInOut,
        ));

        // 開始動畫
        _rotationController.repeat();
        _pulseController.repeat(reverse: true);
        _fadeController.forward();

        // 定期更換載入訊息
        _startMessageRotation();
    }

    void _startMessageRotation() {
        Future.delayed(const Duration(seconds: 3), () {
            if (mounted) {
                setState(() {
                    _currentMessageIndex = (_currentMessageIndex + 1) % _loadingMessages.length;
                });
                _startMessageRotation();
            }
        });
    }

    @override
    void dispose() {
        _rotationController.dispose();
        _pulseController.dispose();
        _fadeController.dispose();
        super.dispose();
    }

    @override
    Widget build(BuildContext context) {
        return Dialog(
            backgroundColor: Colors.transparent,
            child: FadeTransition(
                opacity: _fadeAnimation,
                child: Container(
                    padding: const EdgeInsets.all(32),
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(24),
                        boxShadow: [
                            BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 20,
                                offset: const Offset(0, 10),
                            ),
                        ],
                    ),
                    child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                            // 動畫載入指示器
                            _buildLoadingIndicator(),
                            const SizedBox(height: 24),
                            
                            // 標題
                            const Text(
                                '正在生成圖片',
                                style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: Color(0xFF100D1B),
                                ),
                            ),
                            const SizedBox(height: 12),
                            
                            // 動態訊息
                            AnimatedSwitcher(
                                duration: const Duration(milliseconds: 500),
                                child: Text(
                                    widget.message ?? _loadingMessages[_currentMessageIndex],
                                    key: ValueKey(_currentMessageIndex),
                                    textAlign: TextAlign.center,
                                    style: const TextStyle(
                                        fontSize: 14,
                                        color: Colors.grey,
                                    ),
                                ),
                            ),
                            const SizedBox(height: 24),
                            
                            // 取消按鈕（如果提供了回調）
                            if (widget.onCancel != null)
                                TextButton(
                                    onPressed: widget.onCancel,
                                    child: const Text(
                                        '取消',
                                        style: TextStyle(
                                            color: Colors.grey,
                                            fontSize: 16,
                                        ),
                                    ),
                                ),
                        ],
                    ),
                ),
            ),
        );
    }

    Widget _buildLoadingIndicator() {
        return SizedBox(
            width: 80,
            height: 80,
            child: Stack(
                alignment: Alignment.center,
                children: [
                    // 外圈旋轉動畫
                    AnimatedBuilder(
                        animation: _rotationAnimation,
                        builder: (context, child) {
                            return Transform.rotate(
                                angle: _rotationAnimation.value,
                                child: Container(
                                    width: 80,
                                    height: 80,
                                    decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        border: Border.all(
                                            color: const Color(0xFF3713EC).withOpacity(0.3),
                                            width: 2,
                                        ),
                                    ),
                                    child: CustomPaint(
                                        painter: _LoadingPainter(),
                                    ),
                                ),
                            );
                        },
                    ),
                    
                    // 中心脈衝動畫
                    AnimatedBuilder(
                        animation: _pulseAnimation,
                        builder: (context, child) {
                            return Transform.scale(
                                scale: _pulseAnimation.value,
                                child: Container(
                                    width: 40,
                                    height: 40,
                                    decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: const Color(0xFF3713EC).withOpacity(0.2),
                                    ),
                                    child: const Icon(
                                        Icons.auto_awesome,
                                        color: Color(0xFF3713EC),
                                        size: 20,
                                    ),
                                ),
                            );
                        },
                    ),
                ],
            ),
        );
    }
}

/// 自定義載入指示器畫筆
class _LoadingPainter extends CustomPainter {
    @override
    void paint(Canvas canvas, Size size) {
        final paint = Paint()
            ..color = const Color(0xFF3713EC)
            ..strokeWidth = 3
            ..style = PaintingStyle.stroke
            ..strokeCap = StrokeCap.round;

        final center = Offset(size.width / 2, size.height / 2);
        final radius = size.width / 2 - 2;

        // 繪製部分圓弧
        canvas.drawArc(
            Rect.fromCircle(center: center, radius: radius),
            -math.pi / 2,
            math.pi,
            false,
            paint,
        );
    }

    @override
    bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// 顯示生成載入對話框的便捷方法
Future<void> showGenerationLoadingDialog(
    BuildContext context, {
    String? message,
    VoidCallback? onCancel,
}) {
    return showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => GenerationLoadingDialog(
            message: message,
            onCancel: onCancel,
        ),
    );
}
