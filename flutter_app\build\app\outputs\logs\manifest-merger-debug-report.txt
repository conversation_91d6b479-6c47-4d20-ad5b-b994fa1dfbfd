-- Merging decision tree log ---
application
INJECTED from C:\Users\<USER>\Desktop\nano_banana\flutter_app\android\app\src\main\AndroidManifest.xml:2:5-33:19
INJECTED from C:\Users\<USER>\Desktop\nano_banana\flutter_app\android\app\src\debug\AndroidManifest.xml
MERGED from [:image_picker_android] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:image_picker_android] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6ca1db17970615e3e029777992983e6b\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6ca1db17970615e3e029777992983e6b\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c40b3605ce1f863a0ab38c5cd772a02\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c40b3605ce1f863a0ab38c5cd772a02\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1eb6682c3f06d5937f76fce498ba76c\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1eb6682c3f06d5937f76fce498ba76c\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\acd569caa193870be1c9a7558bcbe87a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\acd569caa193870be1c9a7558bcbe87a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\42d478d6013065b32538b12d3ef7b157\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\42d478d6013065b32538b12d3ef7b157\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad54c48eb38304d58afae8e4a7584e91\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad54c48eb38304d58afae8e4a7584e91\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\nano_banana\flutter_app\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1eb6682c3f06d5937f76fce498ba76c\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from C:\Users\<USER>\Desktop\nano_banana\flutter_app\android\app\src\main\AndroidManifest.xml
manifest
ADDED from C:\Users\<USER>\Desktop\nano_banana\flutter_app\android\app\src\main\AndroidManifest.xml:1:1-45:12
MERGED from C:\Users\<USER>\Desktop\nano_banana\flutter_app\android\app\src\main\AndroidManifest.xml:1:1-45:12
INJECTED from C:\Users\<USER>\Desktop\nano_banana\flutter_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\Desktop\nano_banana\flutter_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\Desktop\nano_banana\flutter_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:image_picker_android] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-34:12
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:path_provider_android] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:sqflite_android] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6ca1db17970615e3e029777992983e6b\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf65acc7f777a00d59a317272535c12d\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d4d4ce414a44a3730885ae01f2fb152\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5509b130785c4f666bc9243b8f09ec0e\transformed\jetified-activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e78032489b765334547e61e6ea7aed3\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d24b8272075e098c083ed9c95fc52958\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ae506c879913254faf8ad4a868cb5691\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d6de01df90fa1cbbf544d2f267f1650c\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\444bb99b45d5f3093a14f2f3c82161dc\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c225580ff9ba548683b0b22a7305e036\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c40b3605ce1f863a0ab38c5cd772a02\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed1f2a84141e93c19d75fdc67842bc1f\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\47080e52f42fb85d5acc0d8bd39630dc\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\364b6d67c783a7ef23e5e4907033002f\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cc7fe6a7e987b9713215c3be18f59936\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\657debf020188ac0cc762afb07718e33\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1eb6682c3f06d5937f76fce498ba76c\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.exifinterface:exifinterface:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f3a4072a977c767013d6d2a75f41ab9a\transformed\exifinterface-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\acd569caa193870be1c9a7558bcbe87a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\42d478d6013065b32538b12d3ef7b157\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f51db27e9bc46f3ecc6c36bd261e5748\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e10bd6e0b3c29c24c447df1b19c41e12\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad54c48eb38304d58afae8e4a7584e91\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f86bb2847f8c1eeeb6a949d522eada3\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d51c9be43c7540b3db5b1d2f2e0cebbc\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38e3f381553bfb1e561f2f5b45f177d5\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b47e8c1975652c03961ca2586e68646\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\f19d59ee3776eb9cd1a214b3d04fe751\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from C:\Users\<USER>\Desktop\nano_banana\flutter_app\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\nano_banana\flutter_app\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\nano_banana\flutter_app\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\nano_banana\flutter_app\android\app\src\main\AndroidManifest.xml:1:11-69
queries
ADDED from C:\Users\<USER>\Desktop\nano_banana\flutter_app\android\app\src\main\AndroidManifest.xml:39:5-44:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from C:\Users\<USER>\Desktop\nano_banana\flutter_app\android\app\src\main\AndroidManifest.xml:40:9-43:18
action#android.intent.action.PROCESS_TEXT
ADDED from C:\Users\<USER>\Desktop\nano_banana\flutter_app\android\app\src\main\AndroidManifest.xml:41:13-72
	android:name
		ADDED from C:\Users\<USER>\Desktop\nano_banana\flutter_app\android\app\src\main\AndroidManifest.xml:41:21-70
data
ADDED from C:\Users\<USER>\Desktop\nano_banana\flutter_app\android\app\src\main\AndroidManifest.xml:42:13-50
	android:mimeType
		ADDED from C:\Users\<USER>\Desktop\nano_banana\flutter_app\android\app\src\main\AndroidManifest.xml:42:19-48
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\nano_banana\flutter_app\android\app\src\debug\AndroidManifest.xml:6:5-66
	android:name
		ADDED from C:\Users\<USER>\Desktop\nano_banana\flutter_app\android\app\src\debug\AndroidManifest.xml:6:22-64
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\nano_banana\flutter_app\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\nano_banana\flutter_app\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\nano_banana\flutter_app\android\app\src\debug\AndroidManifest.xml
MERGED from [:image_picker_android] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:image_picker_android] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6ca1db17970615e3e029777992983e6b\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6ca1db17970615e3e029777992983e6b\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf65acc7f777a00d59a317272535c12d\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf65acc7f777a00d59a317272535c12d\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d4d4ce414a44a3730885ae01f2fb152\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d4d4ce414a44a3730885ae01f2fb152\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5509b130785c4f666bc9243b8f09ec0e\transformed\jetified-activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5509b130785c4f666bc9243b8f09ec0e\transformed\jetified-activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e78032489b765334547e61e6ea7aed3\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e78032489b765334547e61e6ea7aed3\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d24b8272075e098c083ed9c95fc52958\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d24b8272075e098c083ed9c95fc52958\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ae506c879913254faf8ad4a868cb5691\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ae506c879913254faf8ad4a868cb5691\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d6de01df90fa1cbbf544d2f267f1650c\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d6de01df90fa1cbbf544d2f267f1650c\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\444bb99b45d5f3093a14f2f3c82161dc\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\444bb99b45d5f3093a14f2f3c82161dc\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c225580ff9ba548683b0b22a7305e036\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c225580ff9ba548683b0b22a7305e036\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c40b3605ce1f863a0ab38c5cd772a02\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c40b3605ce1f863a0ab38c5cd772a02\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed1f2a84141e93c19d75fdc67842bc1f\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed1f2a84141e93c19d75fdc67842bc1f\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\47080e52f42fb85d5acc0d8bd39630dc\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\47080e52f42fb85d5acc0d8bd39630dc\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\364b6d67c783a7ef23e5e4907033002f\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\364b6d67c783a7ef23e5e4907033002f\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cc7fe6a7e987b9713215c3be18f59936\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cc7fe6a7e987b9713215c3be18f59936\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\657debf020188ac0cc762afb07718e33\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\657debf020188ac0cc762afb07718e33\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1eb6682c3f06d5937f76fce498ba76c\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1eb6682c3f06d5937f76fce498ba76c\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f3a4072a977c767013d6d2a75f41ab9a\transformed\exifinterface-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.exifinterface:exifinterface:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f3a4072a977c767013d6d2a75f41ab9a\transformed\exifinterface-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\acd569caa193870be1c9a7558bcbe87a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\acd569caa193870be1c9a7558bcbe87a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\42d478d6013065b32538b12d3ef7b157\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\42d478d6013065b32538b12d3ef7b157\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f51db27e9bc46f3ecc6c36bd261e5748\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f51db27e9bc46f3ecc6c36bd261e5748\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e10bd6e0b3c29c24c447df1b19c41e12\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e10bd6e0b3c29c24c447df1b19c41e12\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad54c48eb38304d58afae8e4a7584e91\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad54c48eb38304d58afae8e4a7584e91\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f86bb2847f8c1eeeb6a949d522eada3\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f86bb2847f8c1eeeb6a949d522eada3\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d51c9be43c7540b3db5b1d2f2e0cebbc\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d51c9be43c7540b3db5b1d2f2e0cebbc\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38e3f381553bfb1e561f2f5b45f177d5\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38e3f381553bfb1e561f2f5b45f177d5\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b47e8c1975652c03961ca2586e68646\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b47e8c1975652c03961ca2586e68646\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\f19d59ee3776eb9cd1a214b3d04fe751\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\f19d59ee3776eb9cd1a214b3d04fe751\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\nano_banana\flutter_app\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\nano_banana\flutter_app\android\app\src\debug\AndroidManifest.xml
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
	android:resource
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
	android:enabled
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
	tools:ignore
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-40
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
meta-data#photopicker_activity:0:required
ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
	android:value
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\nano_banana\flutter_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6ca1db17970615e3e029777992983e6b\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6ca1db17970615e3e029777992983e6b\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6ca1db17970615e3e029777992983e6b\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6ca1db17970615e3e029777992983e6b\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6ca1db17970615e3e029777992983e6b\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6ca1db17970615e3e029777992983e6b\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c40b3605ce1f863a0ab38c5cd772a02\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\acd569caa193870be1c9a7558bcbe87a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\acd569caa193870be1c9a7558bcbe87a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\42d478d6013065b32538b12d3ef7b157\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\42d478d6013065b32538b12d3ef7b157\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c40b3605ce1f863a0ab38c5cd772a02\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c40b3605ce1f863a0ab38c5cd772a02\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c40b3605ce1f863a0ab38c5cd772a02\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c40b3605ce1f863a0ab38c5cd772a02\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c40b3605ce1f863a0ab38c5cd772a02\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c40b3605ce1f863a0ab38c5cd772a02\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c40b3605ce1f863a0ab38c5cd772a02\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1eb6682c3f06d5937f76fce498ba76c\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1eb6682c3f06d5937f76fce498ba76c\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1eb6682c3f06d5937f76fce498ba76c\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.jacktech.nanobanana.nano_banana.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1eb6682c3f06d5937f76fce498ba76c\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1eb6682c3f06d5937f76fce498ba76c\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1eb6682c3f06d5937f76fce498ba76c\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1eb6682c3f06d5937f76fce498ba76c\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1eb6682c3f06d5937f76fce498ba76c\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.jacktech.nanobanana.nano_banana.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1eb6682c3f06d5937f76fce498ba76c\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1eb6682c3f06d5937f76fce498ba76c\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\acd569caa193870be1c9a7558bcbe87a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\acd569caa193870be1c9a7558bcbe87a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\acd569caa193870be1c9a7558bcbe87a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\acd569caa193870be1c9a7558bcbe87a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\acd569caa193870be1c9a7558bcbe87a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\acd569caa193870be1c9a7558bcbe87a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\acd569caa193870be1c9a7558bcbe87a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\acd569caa193870be1c9a7558bcbe87a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\acd569caa193870be1c9a7558bcbe87a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\acd569caa193870be1c9a7558bcbe87a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\acd569caa193870be1c9a7558bcbe87a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\acd569caa193870be1c9a7558bcbe87a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\acd569caa193870be1c9a7558bcbe87a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\acd569caa193870be1c9a7558bcbe87a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\acd569caa193870be1c9a7558bcbe87a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\acd569caa193870be1c9a7558bcbe87a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\acd569caa193870be1c9a7558bcbe87a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\acd569caa193870be1c9a7558bcbe87a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\acd569caa193870be1c9a7558bcbe87a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\acd569caa193870be1c9a7558bcbe87a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\acd569caa193870be1c9a7558bcbe87a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
