@echo off

echo.
echo ========================================
echo  NanoBanana AI - Android Build and Run
echo  (MAXIMUM GPU Performance - Simple)
echo ========================================
echo.

REM Change to flutter_app directory
cd /d "%~dp0..\flutter_app"

echo [INFO] Current directory: %CD%

REM Check for pubspec.yaml
if not exist "pubspec.yaml" (
    echo [ERROR] Cannot find pubspec.yaml
    goto :error_end
)

echo [SUCCESS] Found pubspec.yaml

echo.
echo [INFO] Step 1: Checking Flutter...
call flutter --version >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Flutter not found, but continuing...
) else (
    echo [SUCCESS] Flutter is available
)

echo.
echo [INFO] Step 2: Cleaning project...
call flutter clean >nul 2>&1
echo [SUCCESS] Project cleaned

echo.
echo [INFO] Step 3: Getting dependencies...
call flutter pub get
if errorlevel 1 (
    echo [WARNING] Some dependencies failed, continuing...
) else (
    echo [SUCCESS] Dependencies installed
)

echo.
echo [INFO] Step 4: Checking emulators...
call flutter emulators
if errorlevel 1 (
    echo [WARNING] Cannot list emulators
)

echo.
echo [INFO] Step 5: Starting emulator with GPU...

REM Try GPU acceleration first
set ANDROID_SDK_ROOT=%LOCALAPPDATA%\Android\Sdk
if not exist "%ANDROID_SDK_ROOT%" (
    set ANDROID_SDK_ROOT=%USERPROFILE%\AppData\Local\Android\Sdk
)

if exist "%ANDROID_SDK_ROOT%\emulator\emulator.exe" (
    echo [INFO] Using GPU acceleration...
    start "" "%ANDROID_SDK_ROOT%\emulator\emulator.exe" -avd Medium_Phone_API_36.0 -gpu host -memory 3072 -cores 2 -no-boot-anim -accel on -feature Vulkan -feature GLDirectMem -feature HostComposition
    echo [SUCCESS] Emulator started with GPU
) else (
    echo [INFO] Using Flutter command...
    call flutter emulators --launch Medium_Phone_API_36.0 >nul 2>&1
    echo [SUCCESS] Emulator started
)

echo.
echo [INFO] Step 6: Waiting 20 seconds...
timeout /t 20 /nobreak >nul

echo.
echo [INFO] Step 7: Running app...
call flutter run --debug

echo.
if errorlevel 1 (
    echo [WARNING] App completed with warnings
) else (
    echo [SUCCESS] App is running!
)

goto :normal_end

:error_end
echo.
echo [ERROR] Script failed - check messages above
pause
exit /b 1

:normal_end
echo.
echo [SUCCESS] Script completed successfully
pause
