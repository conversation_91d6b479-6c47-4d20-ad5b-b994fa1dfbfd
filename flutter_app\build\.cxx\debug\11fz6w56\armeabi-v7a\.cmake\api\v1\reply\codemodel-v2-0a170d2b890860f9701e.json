{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "debug", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/nano_banana/flutter_app/build/.cxx/debug/11fz6w56/armeabi-v7a", "source": "C:/development/flutter/packages/flutter_tools/gradle/src/main/scripts"}, "version": {"major": 2, "minor": 3}}