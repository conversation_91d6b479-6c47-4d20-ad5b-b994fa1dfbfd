import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:go_router/go_router.dart';
import 'dart:io';

class AdaptiveBottomNavigation extends StatelessWidget {
    final int currentIndex;
    final Function(int) onTap;

    const AdaptiveBottomNavigation({
        super.key,
        required this.currentIndex,
        required this.onTap,
    });

    @override
    Widget build(BuildContext context) {
        final items = [
            const BottomNavigationBarItem(
                icon: Icon(Icons.home_outlined),
                activeIcon: Icon(Icons.home),
                label: 'Home',
            ),
            const BottomNavigationBarItem(
                icon: Icon(Icons.history_outlined),
                activeIcon: Icon(Icons.history),
                label: 'History',
            ),
            const BottomNavigationBarItem(
                icon: Icon(Icons.auto_awesome_outlined),
                activeIcon: Icon(Icons.auto_awesome),
                label: 'Generate',
            ),
            const BottomNavigationBarItem(
                icon: Icon(Icons.person_outline),
                activeIcon: Icon(Icons.person),
                label: 'Profile',
            ),
        ];

        if (Platform.isIOS) {
            return CupertinoTabBar(
                currentIndex: currentIndex,
                onTap: (index) {
                    onTap(index);
                    _navigateToPage(context, index);
                },
                backgroundColor: Colors.white.withOpacity(0.9),
                activeColor: const Color(0xFF7B61FF),
                inactiveColor: Colors.grey,
                items: items.map((item) => BottomNavigationBarItem(
                    icon: item.icon,
                    activeIcon: item.activeIcon,
                    label: item.label,
                )).toList(),
            );
        } else {
            return BottomNavigationBar(
                currentIndex: currentIndex,
                onTap: (index) {
                    onTap(index);
                    _navigateToPage(context, index);
                },
                type: BottomNavigationBarType.fixed,
                backgroundColor: Colors.white.withOpacity(0.9),
                selectedItemColor: const Color(0xFF7B61FF),
                unselectedItemColor: Colors.grey,
                elevation: 8,
                items: items,
            );
        }
    }

    void _navigateToPage(BuildContext context, int index) {
        switch (index) {
            case 0:
                context.go('/home');
                break;
            case 1:
                context.go('/history');
                break;
            case 2:
                context.go('/create');
                break;
            case 3:
                context.go('/membership');
                break;
        }
    }
}
