# NanoBanana AI - 完整項目

一個基於 Flutter 的 AI 圖像生成應用程序，支持 iOS 和 Android 平台，採用 iOS-first 設計理念。
發想:
(1)Big Banana🍌 Quick Prompts
https://greasyfork.org/en/scripts/548440-big-banana-quick-prompts
(2)更多的prompt
https://github.com/PicoTrex/Awesome-Nano-Banana-images

## 🏗️ 項目結構

本項目包含三個主要資料夾，每個都有特定的功能和用途：

### 📱 `flutter_app/` - Flutter 應用程序
**主要的 Flutter 應用程序代碼**

- **功能**: 包含完整的 Flutter 應用程序源代碼
- **內容**: 
  - `lib/` - Dart 源代碼（screens、widgets、providers 等）
  - `android/` - Android 平台特定配置
  - `ios/` - iOS 平台特定配置
  - `pubspec.yaml` - Flutter 依賴配置
  - `test/` - 單元測試和 widget 測試
- **技術棧**: Flutter 3.9.2+, Dart 3.0.0+
- **平台支持**: iOS, Android, Web (測試用)

### 🔥 `firebase/` - Firebase 後端服務
**雲端後端和 Firebase Functions**

- **功能**: 提供 AI 圖像生成的後端服務
- **內容**:
  - `functions/` - Firebase Cloud Functions (Node.js)
  - `firebase.json` - Firebase 項目配置
  - `deploy.py` - 自動化部署腳本
- **服務**: 
  - AI 圖像生成 API
  - 用戶認證和授權
  - 數據存儲和管理
- **部署**: 支持自動化部署到 Firebase

### ⚡ `bat/` - Windows 自動化腳本
**Windows 批處理腳本，用於開發和構建自動化**

- **功能**: 簡化 Flutter 應用的開發、構建和運行流程
- **特色**: 
  - 🚀 **GPU 優化**: RTX 4070 + Vulkan + 4GB RAM + 4 CPU cores
  - 🎯 **一鍵運行**: 雙擊即可啟動完整的開發環境
  - 🔧 **智能檢測**: 自動檢測 Android SDK 和模擬器
  - 📱 **多設備支持**: 支持不同尺寸的 Android 模擬器
- **主要腳本**:
  - `run-android.bat` ⭐ - 推薦的一鍵運行腳本
  - `quick-android-run.bat` - 快速啟動腳本
  - `build-and-run-android-simple.bat` - 完整構建腳本
  - `nano_banana_compress.bat` - 項目壓縮腳本

## 🚀 快速開始

### 環境要求
- **Flutter SDK**: 3.9.2 或更高版本
- **Node.js**: 18+ (用於 Firebase Functions)
- **Android Studio**: 包含 Android SDK 和模擬器
- **Windows 10/11**: 用於運行批處理腳本
- **Firebase CLI**: 用於部署後端服務

### 安裝步驟

1. **克隆項目**
```bash
git clone <repository-url>
cd nano_banana
```

2. **安裝 Flutter 依賴**
```bash
cd flutter_app
flutter pub get
```

3. **設置 Firebase**
```bash
cd firebase
npm install
firebase login
```

4. **運行應用程序**

**Windows 用戶（推薦）**:
```bash
# 一鍵運行 - 最簡單的方式
.\bat\run-android.bat

# 或者雙擊 bat\run-android.bat 文件
```

**手動運行**:
```bash
cd flutter_app
flutter run
```

## 📋 開發工作流程

### 1. 前端開發 (Flutter)
```bash
cd flutter_app
flutter run          # 運行應用
flutter test          # 運行測試
flutter build apk     # 構建 Android APK
```

### 2. 後端開發 (Firebase)
```bash
cd firebase
npm run serve         # 本地測試 Functions
firebase deploy       # 部署到 Firebase
```

### 3. 自動化開發 (Windows)
```bash
.\bat\run-android.bat                    # 一鍵運行
.\bat\build-and-run-android-simple.bat   # 完整構建
.\bat\nano_banana_compress.bat           # 項目打包
```

## 🎯 項目特色

### 🎨 AI 圖像生成
- 支持多種 AI 生成風格
- 多圖像輸入和參考
- 實時預覽和調整

### 📱 跨平台支持
- iOS 和 Android 原生體驗
- 響應式設計適配不同屏幕
- Web 版本用於測試

### ⚡ 高性能優化
- GPU 加速的 Android 模擬器
- Vulkan API 圖形渲染
- 智能緩存和預加載

### 🔧 開發者友好
- 一鍵運行腳本
- 自動化構建和部署
- 詳細的錯誤處理和日誌

## 📁 詳細文檔

每個資料夾都包含詳細的 README.md 文件：

- **Flutter 應用**: [`flutter_app/README.md`](flutter_app/README.md)
- **Firebase 後端**: [`firebase/README.md`](firebase/README.md)  
- **批處理腳本**: [`bat/README.md`](bat/README.md)

## 🐛 故障排除

### 常見問題

1. **Flutter 環境問題**
```bash
flutter doctor -v    # 檢查 Flutter 環境
```

2. **Android 模擬器問題**
```bash
.\bat\build-and-run-android.bat    # 使用詳細日誌腳本
```

3. **Firebase 部署問題**
```bash
cd firebase
firebase login       # 重新登錄
firebase use --add   # 選擇項目
```

## 📄 許可證

本項目採用 MIT 許可證 - 查看 [LICENSE](LICENSE) 文件了解詳情。

## 🤝 貢獻

歡迎提交 Pull Request 和 Issue！

## 📞 聯繫方式

如有問題或建議，請通過以下方式聯繫：
- 創建 GitHub Issue
- 發送郵件至開發團隊

---

**NanoBanana AI** - 讓 AI 圖像生成變得簡單而強大！ 🎨✨
