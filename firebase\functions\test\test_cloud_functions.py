#!/usr/bin/env python3
"""
測試 Firebase Cloud Functions 的完整測試程式
測試兩個函數：generate_image_from_text 和 generate_image_with_references
"""

import base64
import json
import os
import requests
import time
from datetime import datetime
from PIL import Image
from io import BytesIO

# Cloud Functions URLs
TEXT_TO_IMAGE_URL = "https://us-central1-nano-banana-60f5a.cloudfunctions.net/generate_image_from_text"
IMAGE_WITH_REFERENCES_URL = "https://us-central1-nano-banana-60f5a.cloudfunctions.net/generate_image_with_references"

# 測試用的 prompt
TEST_PROMPT = "Character emotions sheet, multiple expressions of the provided character, featuring happy, sad, angry, surprised, shy, confused, playful, disgusted, thoughtful, crying, and embarrassed. Full set of emotions, clear and distinct expressions, clean background"

# 輸出資料夾
OUTPUT_DIR = "output"


def ensure_output_dir():
    """確保輸出資料夾存在"""
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)
    print(f"✅ 輸出資料夾準備完成: {OUTPUT_DIR}")


def load_test_image():
    """載入測試圖片並轉換為 base64"""
    test_image_path = "test.png"
    
    if not os.path.exists(test_image_path):
        print(f"❌ 找不到測試圖片: {test_image_path}")
        return None
    
    try:
        with open(test_image_path, "rb") as image_file:
            image_bytes = image_file.read()
            base64_data = base64.b64encode(image_bytes).decode('utf-8')
            print(f"✅ 成功載入測試圖片: {test_image_path}")
            return base64_data
    except Exception as e:
        print(f"❌ 載入測試圖片失敗: {e}")
        return None


def save_image_from_base64(base64_data, filename):
    """從 base64 數據保存圖片到 output 資料夾"""
    try:
        # 移除可能的 data URL 前綴
        if ',' in base64_data:
            base64_data = base64_data.split(',')[1]
        
        # 解碼 base64 數據
        img_bytes = base64.b64decode(base64_data)
        
        # 創建 PIL Image 對象
        img = Image.open(BytesIO(img_bytes))
        
        # 保存到 output 資料夾
        output_path = os.path.join(OUTPUT_DIR, filename)
        img.save(output_path)
        
        print(f"✅ 圖片已保存: {output_path}")
        return True
    except Exception as e:
        print(f"❌ 保存圖片失敗: {e}")
        return False


def test_text_to_image():
    """測試純文字生成圖片功能"""
    print("\n" + "="*60)
    print("🧪 測試 1: 純文字生成圖片")
    print("="*60)
    
    # 準備請求數據
    request_data = {
        "prompt": TEST_PROMPT
    }
    
    print(f"📝 Prompt: {TEST_PROMPT}")
    print(f"🌐 URL: {TEXT_TO_IMAGE_URL}")
    
    try:
        print("⏳ 發送請求...")
        start_time = time.time()
        
        response = requests.post(
            TEXT_TO_IMAGE_URL,
            json=request_data,
            headers={'Content-Type': 'application/json'},
            timeout=120  # 2分鐘超時
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"⏱️  請求耗時: {duration:.2f} 秒")
        print(f"📊 狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                print("🎉 請求成功！")
                
                # 處理文字回應
                if result.get('text'):
                    print(f"💬 API 回應文字: {result['text']}")
                
                # 處理圖片回應
                images = result.get('images', [])
                print(f"🖼️  收到 {len(images)} 張圖片")
                
                for i, img_data in enumerate(images):
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"text_to_image_{timestamp}_{i+1}.jpg"
                    
                    if save_image_from_base64(img_data['data'], filename):
                        print(f"   - 圖片 {i+1}: {filename}")
                
                return True
            else:
                error_msg = result.get('error', '未知錯誤')
                print(f"❌ API 回應錯誤: {error_msg}")
                return False
        else:
            print(f"❌ HTTP 錯誤: {response.status_code}")
            print(f"錯誤內容: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 請求超時")
        return False
    except Exception as e:
        print(f"❌ 請求失敗: {e}")
        return False


def test_image_with_references():
    """測試使用參考圖片生成圖片功能"""
    print("\n" + "="*60)
    print("🧪 測試 2: 使用參考圖片生成圖片")
    print("="*60)
    
    # 載入測試圖片
    test_image_base64 = load_test_image()
    if not test_image_base64:
        print("❌ 無法載入測試圖片，跳過此測試")
        return False
    
    # 準備請求數據
    request_data = {
        "prompt": TEST_PROMPT,
        "images": [
            {
                "data": test_image_base64,
                "mime_type": "image/png"
            }
        ]
    }
    
    print(f"📝 Prompt: {TEST_PROMPT}")
    print(f"🖼️  參考圖片: test.png")
    print(f"🌐 URL: {IMAGE_WITH_REFERENCES_URL}")
    
    try:
        print("⏳ 發送請求...")
        start_time = time.time()
        
        response = requests.post(
            IMAGE_WITH_REFERENCES_URL,
            json=request_data,
            headers={'Content-Type': 'application/json'},
            timeout=120  # 2分鐘超時
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"⏱️  請求耗時: {duration:.2f} 秒")
        print(f"📊 狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                print("🎉 請求成功！")
                
                # 處理文字回應
                if result.get('text'):
                    print(f"💬 API 回應文字: {result['text']}")
                
                # 處理圖片回應
                images = result.get('images', [])
                print(f"🖼️  收到 {len(images)} 張圖片")
                
                for i, img_data in enumerate(images):
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"image_with_ref_{timestamp}_{i+1}.jpg"
                    
                    if save_image_from_base64(img_data['data'], filename):
                        print(f"   - 圖片 {i+1}: {filename}")
                
                return True
            else:
                error_msg = result.get('error', '未知錯誤')
                print(f"❌ API 回應錯誤: {error_msg}")
                return False
        else:
            print(f"❌ HTTP 錯誤: {response.status_code}")
            print(f"錯誤內容: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 請求超時")
        return False
    except Exception as e:
        print(f"❌ 請求失敗: {e}")
        return False


def main():
    """主函數"""
    print("🚀 Firebase Cloud Functions 測試程式")
    print(f"⏰ 測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 確保輸出資料夾存在
    ensure_output_dir()
    
    # 執行測試
    test1_success = test_text_to_image()
    test2_success = test_image_with_references()
    
    # 總結
    print("\n" + "="*60)
    print("📋 測試總結")
    print("="*60)
    print(f"測試 1 (純文字生成): {'✅ 成功' if test1_success else '❌ 失敗'}")
    print(f"測試 2 (參考圖片生成): {'✅ 成功' if test2_success else '❌ 失敗'}")
    
    if test1_success and test2_success:
        print("🎉 所有測試通過！")
    else:
        print("⚠️  部分測試失敗，請檢查錯誤信息")
    
    print(f"📁 生成的圖片保存在: {OUTPUT_DIR} 資料夾")


if __name__ == "__main__":
    main()
