import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'dart:io';
import '../providers/app_state_provider.dart';
import '../widgets/bottom_navigation.dart';
import '../models/generation_history.dart';

class HistoryScreen extends StatefulWidget {
    const HistoryScreen({super.key});

    @override
    State<HistoryScreen> createState() => _HistoryScreenState();
}

class _HistoryScreenState extends State<HistoryScreen> {
    @override
    void initState() {
        super.initState();
        // 加載歷史記錄
        WidgetsBinding.instance.addPostFrameCallback((_) {
            final appState = Provider.of<AppStateProvider>(context, listen: false);
            appState.loadHistoryFromFile();
        });
    }

    @override
    Widget build(BuildContext context) {
        return Scaffold(
            backgroundColor: const Color(0xFFF9F8FC),
            body: SafeArea(
                child: Column(
                    children: [
                        _buildHeader(),
                        Expanded(
                            child: Consumer<AppStateProvider>(
                                builder: (context, appState, child) {
                                    if (!appState.isHistoryLoaded) {
                                        return const Center(
                                            child: CircularProgressIndicator(),
                                        );
                                    }

                                    if (appState.generationHistory.isEmpty) {
                                        return _buildEmptyState();
                                    }

                                    return _buildHistoryList(appState.generationHistory);
                                },
                            ),
                        ),
                    ],
                ),
            ),
            bottomNavigationBar: Consumer<AppStateProvider>(
                builder: (context, appState, child) {
                    return AdaptiveBottomNavigation(
                        currentIndex: 1,
                        onTap: appState.setBottomNavIndex,
                    );
                },
            ),
        );
    }

    Widget _buildHeader() {
        return Container(
            color: const Color(0xFFF9F8FC),
            child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                    children: [
                        IconButton(
                            onPressed: () => context.go('/home'),
                            icon: const Icon(
                                Icons.arrow_back_ios,
                                color: Color(0xFF100D1B),
                            ),
                        ),
                        const Expanded(
                            child: Text(
                                'History',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: Color(0xFF100D1B),
                                ),
                            ),
                        ),
                        IconButton(
                            onPressed: _showClearHistoryDialog,
                            icon: const Icon(
                                Icons.delete_outline,
                                color: Colors.grey,
                            ),
                        ),
                    ],
                ),
            ),
        );
    }

    Widget _buildEmptyState() {
        return Center(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                    Icon(
                        Icons.history,
                        size: 80,
                        color: Colors.grey[300],
                    ),
                    const SizedBox(height: 16),
                    Text(
                        '還沒有生成記錄',
                        style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                            color: Colors.grey[600],
                        ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                        '開始創建您的第一張 AI 圖片吧！',
                        style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[500],
                        ),
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton(
                        onPressed: () => context.go('/create'),
                        style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF3713EC),
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                            ),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 24,
                                vertical: 12,
                            ),
                        ),
                        child: const Text('開始創建'),
                    ),
                ],
            ),
        );
    }

    Widget _buildHistoryList(List<GenerationHistory> history) {
        return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: history.length,
            itemBuilder: (context, index) {
                final item = history[index];
                return _buildHistoryItem(item);
            },
        );
    }

    Widget _buildHistoryItem(GenerationHistory history) {
        return Container(
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                    BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                    ),
                ],
            ),
            child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                        // 頭部信息
                        Row(
                            children: [
                                Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 4,
                                    ),
                                    decoration: BoxDecoration(
                                        color: history.status.color.withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Text(
                                        history.status.displayText,
                                        style: TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.w500,
                                            color: history.status.color,
                                        ),
                                    ),
                                ),
                                const Spacer(),
                                Text(
                                    history.formattedTime,
                                    style: const TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey,
                                    ),
                                ),
                            ],
                        ),
                        const SizedBox(height: 12),
                        
                        // Prompt
                        Text(
                            'Prompt:',
                            style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: Color(0xFF100D1B),
                            ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                            history.prompt,
                            style: const TextStyle(
                                fontSize: 14,
                                color: Colors.grey,
                            ),
                            maxLines: 3,
                            overflow: TextOverflow.ellipsis,
                        ),
                        
                        // 參考圖片
                        if (history.hasReferenceImages) ...[
                            const SizedBox(height: 12),
                            Text(
                                'Reference Images (${history.referenceImages.length}):',
                                style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: Color(0xFF100D1B),
                                ),
                            ),
                            const SizedBox(height: 8),
                            SizedBox(
                                height: 60,
                                child: ListView.builder(
                                    scrollDirection: Axis.horizontal,
                                    itemCount: history.referenceImages.length,
                                    itemBuilder: (context, index) {
                                        return Container(
                                            margin: const EdgeInsets.only(right: 8),
                                            width: 60,
                                            height: 60,
                                            decoration: BoxDecoration(
                                                borderRadius: BorderRadius.circular(8),
                                                image: DecorationImage(
                                                    image: FileImage(File(history.referenceImages[index])),
                                                    fit: BoxFit.cover,
                                                ),
                                            ),
                                        );
                                    },
                                ),
                            ),
                        ],
                        
                        // 生成結果
                        if (history.generatedImages.isNotEmpty) ...[
                            const SizedBox(height: 12),
                            Text(
                                'Generated Images (${history.generatedImages.length}):',
                                style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: Color(0xFF100D1B),
                                ),
                            ),
                            const SizedBox(height: 8),
                            SizedBox(
                                height: 80,
                                child: ListView.builder(
                                    scrollDirection: Axis.horizontal,
                                    itemCount: history.generatedImages.length,
                                    itemBuilder: (context, index) {
                                        final image = history.generatedImages[index];
                                        return GestureDetector(
                                            onTap: () => _showImageDialog(image),
                                            child: Container(
                                                margin: const EdgeInsets.only(right: 8),
                                                width: 80,
                                                height: 80,
                                                decoration: BoxDecoration(
                                                    borderRadius: BorderRadius.circular(8),
                                                    image: DecorationImage(
                                                        image: FileImage(File(image.localPath)),
                                                        fit: BoxFit.cover,
                                                    ),
                                                ),
                                            ),
                                        );
                                    },
                                ),
                            ),
                        ],
                        
                        // 錯誤信息
                        if (history.status == GenerationStatus.failed && history.errorMessage != null) ...[
                            const SizedBox(height: 12),
                            Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                    color: Colors.red.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(8),
                                ),
                                child: Row(
                                    children: [
                                        const Icon(
                                            Icons.error_outline,
                                            color: Colors.red,
                                            size: 16,
                                        ),
                                        const SizedBox(width: 8),
                                        Expanded(
                                            child: Text(
                                                history.errorMessage!,
                                                style: const TextStyle(
                                                    fontSize: 12,
                                                    color: Colors.red,
                                                ),
                                            ),
                                        ),
                                    ],
                                ),
                            ),
                        ],
                    ],
                ),
            ),
        );
    }

    void _showImageDialog(GeneratedImage image) {
        showDialog(
            context: context,
            builder: (context) => Dialog(
                backgroundColor: Colors.transparent,
                child: GestureDetector(
                    onTap: () => Navigator.of(context).pop(),
                    child: Container(
                        constraints: BoxConstraints(
                            maxHeight: MediaQuery.of(context).size.height * 0.8,
                            maxWidth: MediaQuery.of(context).size.width * 0.9,
                        ),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16),
                            image: DecorationImage(
                                image: FileImage(File(image.localPath)),
                                fit: BoxFit.contain,
                            ),
                        ),
                    ),
                ),
            ),
        );
    }

    void _showClearHistoryDialog() {
        showDialog(
            context: context,
            builder: (context) => AlertDialog(
                title: const Text('清空歷史記錄'),
                content: const Text('確定要清空所有歷史記錄嗎？此操作無法撤銷。'),
                actions: [
                    TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text('取消'),
                    ),
                    TextButton(
                        onPressed: () async {
                            Navigator.of(context).pop();
                            final appState = Provider.of<AppStateProvider>(context, listen: false);
                            await appState.clearGenerationHistory();
                            if (mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                        content: Text('歷史記錄已清空'),
                                    ),
                                );
                            }
                        },
                        child: const Text(
                            '確定',
                            style: TextStyle(color: Colors.red),
                        ),
                    ),
                ],
            ),
        );
    }
}
