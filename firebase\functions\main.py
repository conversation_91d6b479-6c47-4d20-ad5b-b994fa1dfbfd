# Nano Banana Firebase Cloud Functions
# 安全地處理 Gemini API 調用，避免在客戶端暴露 API Key

import base64
import json
import os
from io import BytesIO
from typing import Any, Dict, List, Optional

from firebase_functions import https_fn
from firebase_functions.options import set_global_options
from firebase_admin import initialize_app
from google import genai
from google.genai import types
from PIL import Image

# 初始化 Firebase Admin
initialize_app()

# 設置全局選項
set_global_options(max_instances=10)

# Gemini API 配置
GEMINI_API_KEY = "AIzaSyB3o41KGHFr-j4gFufq9zkdKzFBIJ1Z4YY"
GEMINI_MODEL = "gemini-2.5-flash-image-preview"


def create_cors_response(data: Any, status_code: int = 200) -> https_fn.Response:
    """創建帶有 CORS 標頭的響應"""
    headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Content-Type': 'application/json'
    }

    return https_fn.Response(
        response=json.dumps(data, ensure_ascii=False),
        status=status_code,
        headers=headers
    )


def validate_request_data(data: Dict[str, Any]) -> tuple[bool, str]:
    """驗證請求數據"""
    if not data:
        return False, "請求數據為空"

    if 'prompt' not in data or not data['prompt'].strip():
        return False, "prompt 參數是必需的"

    return True, ""


def process_base64_image(base64_data: str) -> Image.Image:
    """處理 base64 圖片數據"""
    try:
        # 移除 data URL 前綴（如果存在）
        if ',' in base64_data:
            base64_data = base64_data.split(',')[1]

        # 解碼 base64 數據
        image_bytes = base64.b64decode(base64_data)

        # 創建 PIL Image 對象
        image = Image.open(BytesIO(image_bytes))

        return image
    except Exception as e:
        raise ValueError(f"無法處理圖片數據: {str(e)}")


@https_fn.on_request()
def generate_image_from_text(req: https_fn.Request) -> https_fn.Response:
    """
    純文字生成圖片的 Cloud Function

    請求格式:
    {
        "prompt": "描述要生成的圖片"
    }
    """

    # 處理 OPTIONS 請求（CORS 預檢）
    if req.method == 'OPTIONS':
        return create_cors_response({})

    # 只接受 POST 請求
    if req.method != 'POST':
        return create_cors_response(
            {"error": "只支持 POST 請求"},
            status_code=405
        )

    try:
        # 解析請求數據
        request_data = req.get_json()

        # 驗證請求數據
        is_valid, error_message = validate_request_data(request_data)
        if not is_valid:
            return create_cors_response(
                {"error": error_message},
                status_code=400
            )

        prompt = request_data['prompt']

        # 初始化 Gemini 客戶端
        client = genai.Client(api_key=GEMINI_API_KEY)

        # 構建請求內容
        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_text(text=prompt),
                ],
            ),
        ]

        # 配置生成參數
        generate_content_config = types.GenerateContentConfig(
            response_modalities=[
                "IMAGE",
                "TEXT",
            ],
        )

        # 調用 Gemini API
        response = client.models.generate_content(
            model=GEMINI_MODEL,
            contents=contents,
            config=generate_content_config,
        )

        # 處理響應
        result = {"success": True, "images": [], "text": ""}

        if response.candidates and len(response.candidates) > 0:
            candidate = response.candidates[0]
            if candidate.content and candidate.content.parts:
                for part in candidate.content.parts:
                    # 處理文字響應
                    if part.text:
                        result["text"] = part.text

                    # 處理圖片響應
                    if part.inline_data and part.inline_data.data:
                        image_data = {
                            "data": base64.b64encode(part.inline_data.data).decode('utf-8'),
                            "mime_type": part.inline_data.mime_type
                        }
                        result["images"].append(image_data)

        return create_cors_response(result)

    except Exception as e:
        return create_cors_response(
            {"error": f"生成圖片時發生錯誤: {str(e)}"},
            status_code=500
        )


@https_fn.on_request()
def generate_image_with_references(req: https_fn.Request) -> https_fn.Response:
    """
    使用參考圖片生成圖片的 Cloud Function

    請求格式:
    {
        "prompt": "描述要生成的圖片",
        "images": [
            {
                "data": "base64編碼的圖片數據",
                "mime_type": "image/jpeg"
            }
        ]
    }
    """

    # 處理 OPTIONS 請求（CORS 預檢）
    if req.method == 'OPTIONS':
        return create_cors_response({})

    # 只接受 POST 請求
    if req.method != 'POST':
        return create_cors_response(
            {"error": "只支持 POST 請求"},
            status_code=405
        )

    try:
        # 解析請求數據
        request_data = req.get_json()

        # 驗證請求數據
        is_valid, error_message = validate_request_data(request_data)
        if not is_valid:
            return create_cors_response(
                {"error": error_message},
                status_code=400
            )

        prompt = request_data['prompt']
        images_data = request_data.get('images', [])

        if not images_data:
            return create_cors_response(
                {"error": "至少需要提供一張參考圖片"},
                status_code=400
            )

        # 初始化 Gemini 客戶端
        client = genai.Client(api_key=GEMINI_API_KEY)

        # 構建請求內容
        parts = [types.Part.from_text(text=prompt)]

        # 處理參考圖片
        for img_data in images_data:
            try:
                if 'data' not in img_data:
                    continue

                # 直接使用 base64 數據創建內聯數據部分
                base64_data = img_data['data']
                # 移除可能的 data URL 前綴
                if ',' in base64_data:
                    base64_data = base64_data.split(',')[1]

                # 解碼 base64 數據
                image_bytes = base64.b64decode(base64_data)

                # 創建內聯數據部分
                parts.append(types.Part.from_bytes(
                    data=image_bytes,
                    mime_type=img_data.get('mime_type', 'image/png')
                ))

            except Exception as e:
                return create_cors_response(
                    {"error": f"處理參考圖片時發生錯誤: {str(e)}"},
                    status_code=400
                )

        contents = [
            types.Content(
                role="user",
                parts=parts,
            ),
        ]

        # 配置生成參數
        generate_content_config = types.GenerateContentConfig(
            response_modalities=[
                "IMAGE",
                "TEXT",
            ],
        )

        # 調用 Gemini API
        response = client.models.generate_content(
            model=GEMINI_MODEL,
            contents=contents,
            config=generate_content_config,
        )

        # 處理響應
        result = {"success": True, "images": [], "text": ""}

        if response.candidates and len(response.candidates) > 0:
            candidate = response.candidates[0]
            if candidate.content and candidate.content.parts:
                for part in candidate.content.parts:
                    # 處理文字響應
                    if part.text:
                        result["text"] = part.text

                    # 處理圖片響應
                    if part.inline_data and part.inline_data.data:
                        image_data = {
                            "data": base64.b64encode(part.inline_data.data).decode('utf-8'),
                            "mime_type": part.inline_data.mime_type
                        }
                        result["images"].append(image_data)

        return create_cors_response(result)

    except Exception as e:
        return create_cors_response(
            {"error": f"生成圖片時發生錯誤: {str(e)}"},
            status_code=500
        )