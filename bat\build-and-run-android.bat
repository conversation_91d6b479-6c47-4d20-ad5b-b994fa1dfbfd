@echo off

echo.
echo ========================================
echo  NanoBanana AI - Android Build and Run
echo  (MAXIMUM GPU Performance - Complete)
echo ========================================
echo.

REM Set color
color 0A 2>nul

REM Change to project root
cd /d "%~dp0.."

echo [INFO] Current directory: %CD%

REM Check for pubspec.yaml
if not exist "pubspec.yaml" (
    echo [ERROR] Cannot find pubspec.yaml
    echo [ERROR] Expected structure:
    echo [ERROR]   your_project\
    echo [ERROR]   |- bat\
    echo [ERROR]   |  \- build-and-run-android.bat
    echo [ERROR]   |- lib\
    echo [ERROR]   \- pubspec.yaml
    goto :error_end
)

echo [SUCCESS] Found pubspec.yaml - in correct directory

echo.
echo [INFO] Checking Flutter environment...
echo [DEBUG] Running: flutter doctor --version
flutter doctor --version 2>nul
set FLUTTER_CHECK_RESULT=%ERRORLEVEL%
echo [DEBUG] Flutter check result: %FLUTTER_CHECK_RESULT%
if %FLUTTER_CHECK_RESULT% neq 0 (
    echo [WARNING] Flutter doctor command failed, but continuing...
    echo [WARNING] Please ensure Flutter SDK is installed and in PATH
    echo [WARNING] You can run 'flutter doctor' manually to check
    REM Don't exit here, continue with the script
) else (
    echo [SUCCESS] Flutter environment check passed
)

echo.
echo [INFO] Cleaning project cache...
echo [DEBUG] Running: flutter clean
flutter clean 2>nul
set CLEAN_RESULT=%ERRORLEVEL%
echo [DEBUG] Clean result: %CLEAN_RESULT%
if %CLEAN_RESULT% neq 0 (
    echo [WARNING] Clean command had warnings, continuing...
) else (
    echo [SUCCESS] Project cleaned successfully
)

echo.
echo [INFO] Installing project dependencies...
echo [DEBUG] Running: flutter pub get
flutter pub get
set PUB_GET_RESULT=%ERRORLEVEL%
echo [DEBUG] Pub get result: %PUB_GET_RESULT%
if %PUB_GET_RESULT% neq 0 (
    echo [WARNING] Failed to install some dependencies, but continuing...
    echo [WARNING] You may need to run 'flutter pub get' manually
) else (
    echo [SUCCESS] Dependencies installed successfully
)

echo.
echo [INFO] Checking available Android emulators...
flutter emulators
if %ERRORLEVEL% neq 0 (
    echo [ERROR] Cannot get emulator list
    echo [ERROR] Please ensure Android SDK is properly installed
    pause
    exit /b 1
)

echo.
echo [INFO] Checking current connected devices...
flutter devices

echo.
echo [INFO] Attempting to start Android emulator with MAXIMUM GPU performance...

REM Try to launch emulator with GPU acceleration using Android SDK tools directly
REM First, try to find the Android SDK path
set ANDROID_SDK_ROOT=%LOCALAPPDATA%\Android\Sdk
if not exist "%ANDROID_SDK_ROOT%" (
    set ANDROID_SDK_ROOT=%USERPROFILE%\AppData\Local\Android\Sdk
)

echo [INFO] Checking Android SDK paths...
echo [INFO] Trying: %LOCALAPPDATA%\Android\Sdk
echo [INFO] Trying: %USERPROFILE%\AppData\Local\Android\Sdk

if not exist "%ANDROID_SDK_ROOT%" (
    echo [WARNING] Android SDK not found in default locations
    echo [WARNING] Falling back to Flutter emulator command...

    REM Try to launch Medium_Phone_API_36.0 first
    flutter emulators --launch Medium_Phone_API_36.0 >nul 2>&1
    if %ERRORLEVEL% equ 0 (
        echo [SUCCESS] Emulator Medium_Phone_API_36.0 started successfully
        set EMULATOR_LAUNCHED=1
    ) else (
        echo [WARNING] Cannot start Medium_Phone_API_36.0, trying other emulators...

        REM Try to launch any available emulator
        for /f "tokens=1" %%i in ('flutter emulators ^| findstr "^[A-Za-z]"') do (
            echo [INFO] Trying to start emulator: %%i
            flutter emulators --launch %%i >nul 2>&1
            if not errorlevel 1 (
                echo [SUCCESS] Emulator %%i started successfully
                set EMULATOR_LAUNCHED=1
                goto :emulator_started
            )
        )
    )
) else (
    echo [SUCCESS] Found Android SDK at: %ANDROID_SDK_ROOT%
    echo [INFO] Checking emulator executable...

    if not exist "%ANDROID_SDK_ROOT%\emulator\emulator.exe" (
        echo [WARNING] emulator.exe not found, falling back to Flutter command...
        flutter emulators --launch Medium_Phone_API_36.0 >nul 2>&1
        if %ERRORLEVEL% equ 0 (
            echo [SUCCESS] Emulator started successfully
            set EMULATOR_LAUNCHED=1
        )
    ) else (
        echo [SUCCESS] Found emulator at: %ANDROID_SDK_ROOT%\emulator\emulator.exe
        echo [INFO] Launching emulator with MAXIMUM GPU performance...
        echo [INFO] GPU Settings: RTX 4070 Host GPU + Vulkan + 3GB RAM + 2 CPU cores + Advanced Features

        REM Launch emulator with MAXIMUM GPU acceleration and performance optimizations
        echo [INFO] Starting emulator with command:
        echo [INFO] "%ANDROID_SDK_ROOT%\emulator\emulator.exe" -avd Medium_Phone_API_36.0 -gpu host -memory 3072 -cores 2 -no-boot-anim -accel on -feature Vulkan -feature GLDirectMem -feature HostComposition

        REM Use full path and start command to launch in background
        start "" "%ANDROID_SDK_ROOT%\emulator\emulator.exe" -avd Medium_Phone_API_36.0 -gpu host -memory 3072 -cores 2 -no-boot-anim -accel on -feature Vulkan -feature GLDirectMem -feature HostComposition

        echo [SUCCESS] Emulator started with maximum GPU performance
        set EMULATOR_LAUNCHED=1
    )
)

:emulator_started
if not defined EMULATOR_LAUNCHED (
    echo [ERROR] Cannot start any Android emulator
    echo [ERROR] Please manually start an emulator or connect an Android device
    pause
    exit /b 1
)

REM Wait for emulator to fully start
echo.
echo [INFO] Waiting for emulator to fully start...
timeout /t 15 /nobreak >nul

REM Check device availability
echo [INFO] Checking device connection status...
:check_device
flutter devices | findstr "android" >nul
if %ERRORLEVEL% neq 0 (
    echo [INFO] Waiting for Android device connection...
    timeout /t 5 /nobreak >nul
    goto :check_device
)

echo [SUCCESS] Android device is connected!

REM Build and run the app
echo.
echo [INFO] Starting to build and run NanoBanana AI app...
echo [INFO] This may take several minutes, please be patient...
echo.

REM Run the app (Flutter will auto-select Android device)
flutter run --debug

REM Check run result
if %ERRORLEVEL% equ 0 (
    echo.
    echo [SUCCESS] ========================================
    echo [SUCCESS]  NanoBanana AI app is running successfully!
    echo [SUCCESS] ========================================
    echo [SUCCESS] 
    echo [SUCCESS] App has started in Android emulator
    echo [SUCCESS] You can test these features:
    echo [SUCCESS] - Splash screen with animations
    echo [SUCCESS] - Plugin marketplace browsing
    echo [SUCCESS] - Image creation functionality
    echo [SUCCESS] - Membership center
    echo [SUCCESS] - Settings page
    echo [SUCCESS] 
    echo [SUCCESS] Hot reload shortcuts:
    echo [SUCCESS] - r: Hot Reload
    echo [SUCCESS] - R: Hot Restart
    echo [SUCCESS] - q: Quit application
    echo [SUCCESS] ========================================
) else (
    echo.
    echo [ERROR] ========================================
    echo [ERROR]  Application failed to run!
    echo [ERROR] ========================================
    echo [ERROR] 
    echo [ERROR] Possible solutions:
    echo [ERROR] 1. Check Android SDK installation
    echo [ERROR] 2. Ensure emulator has sufficient storage space
    echo [ERROR] 3. Try restarting the emulator
    echo [ERROR] 4. Run 'flutter doctor' to check environment
    echo [ERROR] 5. Clean project: flutter clean
    echo [ERROR] 6. Check if antivirus is blocking Flutter
    echo [ERROR] ========================================
)

goto :normal_end

:error_end
echo.
echo ========================================
echo  ERROR OCCURRED
echo ========================================
echo.
echo Please check the error messages above.
echo Make sure:
echo 1. Flutter SDK is installed and in PATH
echo 2. Android SDK is installed
echo 3. This script is in the bat/ folder
echo 4. pubspec.yaml exists in project root
echo 5. Run 'flutter doctor' to check environment
echo 6. Check if antivirus is blocking Flutter
echo 7. Try running as administrator
echo.
pause
exit /b 1

:normal_end
echo.
echo ========================================
echo  SCRIPT COMPLETED
echo ========================================
pause
