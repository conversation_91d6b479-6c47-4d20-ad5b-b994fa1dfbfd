@echo off
REM Get the directory where this batch file is located
set SCRIPT_DIR=%~dp0
REM Go to flutter_app directory (since script is in bat/ folder)
set FLUTTER_APP_DIR=%SCRIPT_DIR%..\flutter_app
REM Change to flutter_app directory
cd /d "%FLUTTER_APP_DIR%"

echo ========================================
echo  NanoBanana AI - Quick Android Run
echo  (Small Phone - MAXIMUM GPU Performance)
echo ========================================
echo Current directory: %CD%
echo.
echo This script will:
echo 1. Clean and get Flutter dependencies
echo 2. Check available Android emulators
echo 3. Launch SMALL PHONE emulator with MAXIMUM GPU performance
echo 4. Run the Flutter app
echo.
echo Performance Features:
echo - Host GPU acceleration (RTX 4070 detected)
echo - Vulkan API support for advanced graphics
echo - 3GB RAM allocation (optimized for small screen)
echo - 2 CPU cores for efficient performance
echo - Hardware OpenGL ES 3.0+ support
echo - Direct memory access optimization
echo - Host composition for UI rendering
echo.

REM Check if pubspec.yaml exists
if not exist "pubspec.yaml" (
    echo ERROR: Cannot find pubspec.yaml file in: %CD%
    echo ERROR: Please ensure this script is in the bat/ folder of your Flutter project
    pause
    exit /b 1
)

echo Step 1: Clean and get dependencies...
call flutter clean
call flutter pub get

echo.
echo Step 2: Check available emulators...
call flutter emulators

echo.
echo Step 3: Launch SMALL PHONE emulator with GPU acceleration...
echo Starting Android emulator with hardware acceleration for better performance...

REM Try to launch emulator with GPU acceleration using Android SDK tools directly
REM First, try to find the Android SDK path
set ANDROID_SDK_ROOT=%LOCALAPPDATA%\Android\Sdk
if not exist "%ANDROID_SDK_ROOT%" (
    set ANDROID_SDK_ROOT=%USERPROFILE%\AppData\Local\Android\Sdk
)

echo Checking Android SDK paths...
echo Trying: %LOCALAPPDATA%\Android\Sdk
echo Trying: %USERPROFILE%\AppData\Local\Android\Sdk

REM Define possible small phone emulator names (including the exact one found)
set SMALL_EMULATOR_NAMES=Small_Phone Small_Phone_API_36.0 Pixel_3a_API_36.0 Small_Phone_API_35.0 Pixel_4a_API_36.0 Small_Phone_API_34.0

if not exist "%ANDROID_SDK_ROOT%" (
    echo Warning: Android SDK not found in default locations
    echo Falling back to Flutter emulator command...
    echo Trying to launch small phone emulator...
    
    REM Try different small phone emulator names with Flutter
    for %%i in (%SMALL_EMULATOR_NAMES%) do (
        echo Trying emulator: %%i
        start /b flutter emulators --launch %%i
        goto :emulator_launched
    )
    
    echo Warning: No small phone emulator found, using default medium phone
    start /b flutter emulators --launch Medium_Phone_API_36.0
    
) else (
    echo Found Android SDK at: %ANDROID_SDK_ROOT%
    echo Checking emulator executable...
    
    if not exist "%ANDROID_SDK_ROOT%\emulator\emulator.exe" (
        echo Warning: emulator.exe not found at: %ANDROID_SDK_ROOT%\emulator\emulator.exe
        echo Falling back to Flutter emulator command...
        
        REM Try different small phone emulator names with Flutter
        for %%i in (%SMALL_EMULATOR_NAMES%) do (
            echo Trying emulator: %%i
            start /b flutter emulators --launch %%i
            goto :emulator_launched
        )
        
        echo Warning: No small phone emulator found, using default medium phone
        start /b flutter emulators --launch Medium_Phone_API_36.0
        
    ) else (
        echo Found emulator at: %ANDROID_SDK_ROOT%\emulator\emulator.exe
        echo Launching SMALL PHONE emulator with MAXIMUM GPU performance...
        echo GPU Settings: RTX 4070 Host GPU + Vulkan + 3GB RAM + 2 CPU cores + Advanced Features
        
        REM Change to emulator directory first
        cd /d "%ANDROID_SDK_ROOT%\emulator"
        
        REM Try to launch small phone emulator with maximum performance
        set EMULATOR_LAUNCHED=false
        for %%i in (%SMALL_EMULATOR_NAMES%) do (
            echo Checking for emulator: %%i
            emulator.exe -list-avds | findstr /C:"%%i" >nul
            if not errorlevel 1 (
                echo Found small phone emulator: %%i
                echo Starting emulator with MAXIMUM GPU performance settings...
                start /b emulator.exe -avd %%i -gpu host -memory 3072 -cores 2 -no-boot-anim -accel on -feature Vulkan -feature GLDirectMem -feature HostComposition
                set EMULATOR_LAUNCHED=true
                goto :emulator_launched_sdk
            )
        )
        
        REM If no small phone emulator found, use medium phone with small screen settings
        if "%EMULATOR_LAUNCHED%"=="false" (
            echo Warning: No small phone emulator found
            echo Using Medium Phone with small screen optimization...
            start /b emulator.exe -avd Medium_Phone_API_36.0 -gpu host -memory 2048 -cores 2 -no-boot-anim -accel on -feature Vulkan -feature GLDirectMem -feature HostComposition -skin 720x1280
        )
        
        :emulator_launched_sdk
        REM Return to flutter_app directory
        cd /d "%FLUTTER_APP_DIR%"
    )
)

:emulator_launched

echo.
echo Step 4: Wait for emulator to boot (20 seconds)...
echo GPU acceleration enabled for smoother performance on small screen...
timeout /t 20 /nobreak

echo.
echo Step 5: Run app on Android...
call flutter run

echo.
echo ========================================
echo Script completed!
echo.
echo SMALL PHONE - MAXIMUM GPU Performance Features Enabled:
echo - Host GPU acceleration (RTX 4070/Intel UHD 770)
echo - Vulkan API support for advanced graphics
echo - 3GB RAM allocation (optimized for small screen)
echo - 2 CPU cores for efficient performance
echo - Hardware OpenGL ES 3.0+ support
echo - Direct memory access optimization
echo - Host composition for UI rendering
echo - Small screen optimization (720x1280 or smaller)
echo.
echo Your SMALL PHONE emulator should now have MAXIMUM scrolling performance!
echo Perfect for testing the 105 AI plugins on smaller screens!
echo ========================================
pause
