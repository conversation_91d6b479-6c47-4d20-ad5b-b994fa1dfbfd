@echo off
setlocal enabledelayedexpansion

REM Get script directory and change to flutter_app directory
set SCRIPT_DIR=%~dp0
cd /d "%SCRIPT_DIR%..\flutter_app"

echo ========================================
echo  NanoBanana AI - Android Runner
echo  (MAXIMUM GPU Performance Mode)
echo ========================================
echo Current directory: %CD%

REM Check if pubspec.yaml exists
if not exist "pubspec.yaml" (
    echo ERROR: Cannot find pubspec.yaml
    echo Make sure this script is in the bat folder
    pause
    exit /b 1
)

echo SUCCESS: Found pubspec.yaml

echo.
echo Step 1: Clean and get dependencies...
call flutter clean
call flutter pub get

echo.
echo Step 2: Check emulators...
call flutter emulators

echo.
echo Step 3: Launch emulator with MAXIMUM GPU performance...

REM Try to launch emulator with GPU acceleration
set ANDROID_SDK_ROOT=%LOCALAPPDATA%\Android\Sdk
if not exist "%ANDROID_SDK_ROOT%" (
    set ANDROID_SDK_ROOT=%USERPROFILE%\AppData\Local\Android\Sdk
)

if not exist "%ANDROID_SDK_ROOT%" (
    echo WARNING: Android SDK not found, using Flutter command...
    start /b flutter emulators --launch Medium_Phone_API_36.0
) else (
    echo Found Android SDK at: %ANDROID_SDK_ROOT%

    if not exist "%ANDROID_SDK_ROOT%\emulator\emulator.exe" (
        echo WARNING: emulator.exe not found, using Flutter command...
        start /b flutter emulators --launch Medium_Phone_API_36.0
    ) else (
        echo Launching emulator with MAXIMUM GPU performance...
        echo GPU Settings: RTX 4070 Host GPU + Vulkan + 4GB RAM + 4 CPU cores

        REM Change to emulator directory
        cd /d "%ANDROID_SDK_ROOT%\emulator"

        REM Launch with maximum GPU performance
        start /b emulator.exe -avd Medium_Phone_API_36.0 -gpu host -memory 4096 -cores 4 -no-boot-anim -accel on -feature Vulkan -feature GLDirectMem -feature HostComposition

        REM Return to flutter_app directory
        cd /d "%SCRIPT_DIR%..\flutter_app"
    )
)

echo.
echo Step 4: Wait 20 seconds for GPU-optimized startup...
timeout /t 20 /nobreak

echo.
echo Step 5: Run app...
call flutter run

echo.
echo Done!
pause
